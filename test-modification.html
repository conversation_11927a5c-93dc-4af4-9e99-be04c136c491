<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Test Modification Client</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f0f0f0; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #f9f9f9; }
        .result { margin: 10px 0; padding: 15px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { padding: 12px 24px; margin: 8px; border: none; border-radius: 6px; cursor: pointer; font-weight: bold; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        input, select, textarea { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px; }
        .form-group { margin: 15px 0; }
        label { font-weight: bold; display: block; margin-bottom: 5px; }
        h1 { color: #333; text-align: center; }
        h3 { color: #495057; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Test Modification Client</h1>
        
        <!-- Sélection du client -->
        <div class="test-section">
            <h3>1. Sélection du Client</h3>
            <button class="btn-primary" onclick="loadClients()">📋 Charger Liste Clients</button>
            <div id="clientsList"></div>
            <div id="loadResult" class="result"></div>
        </div>

        <!-- Formulaire de modification -->
        <div class="test-section">
            <h3>2. Modification du Client</h3>
            <div id="editForm" style="display: none;">
                <div class="form-group">
                    <label>ID Client:</label>
                    <input type="text" id="clientId" readonly>
                </div>
                <div class="form-group">
                    <label>Nom:</label>
                    <input type="text" id="nom" placeholder="Nom">
                </div>
                <div class="form-group">
                    <label>Prénom:</label>
                    <input type="text" id="prenom" placeholder="Prénom">
                </div>
                <div class="form-group">
                    <label>Email:</label>
                    <input type="email" id="email" placeholder="Email">
                </div>
                <div class="form-group">
                    <label>Téléphone:</label>
                    <input type="tel" id="telephone" placeholder="Téléphone">
                </div>
                <div class="form-group">
                    <label>Ville:</label>
                    <input type="text" id="ville" placeholder="Ville">
                </div>
                <div class="form-group">
                    <label>Mobile 1:</label>
                    <input type="tel" id="mobile_1" placeholder="Mobile 1">
                </div>
                <div class="form-group">
                    <label>Nom en Arabe:</label>
                    <input type="text" id="nom_arabe" placeholder="الاسم" dir="rtl">
                </div>
                
                <button class="btn-success" onclick="saveClient()">💾 Enregistrer Modifications</button>
                <button class="btn-warning" onclick="resetForm()">🔄 Réinitialiser</button>
            </div>
            <div id="editResult" class="result"></div>
        </div>

        <!-- Test de vérification -->
        <div class="test-section">
            <h3>3. Vérification</h3>
            <button class="btn-primary" onclick="verifyChanges()">🔍 Vérifier Modifications</button>
            <div id="verifyResult" class="result"></div>
        </div>

        <!-- Liens -->
        <div class="test-section">
            <h3>🔗 Accès Interface Principale</h3>
            <div style="text-align: center;">
                <button class="btn-primary" onclick="window.open('/', '_blank')" style="padding: 15px 30px; font-size: 16px;">
                    🖥️ Ouvrir Interface Principale
                </button>
            </div>
        </div>
    </div>

    <script>
        let currentClientId = null;
        let originalClient = null;

        // Charger la liste des clients
        async function loadClients() {
            try {
                addResult('loadResult', 'info', 'Chargement des clients...');
                
                const response = await fetch('/api/clients');
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                
                const clients = await response.json();
                
                let html = '<h4>Clients disponibles:</h4>';
                clients.forEach(client => {
                    html += `
                        <div style="border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 5px; background: white;">
                            <strong>${client.nom} ${client.prenom}</strong> (${client.email})
                            <button class="btn-warning" onclick="selectClient(${client.id})" style="float: right; padding: 5px 10px;">
                                ✏️ Modifier
                            </button>
                            <div style="clear: both;"></div>
                        </div>
                    `;
                });
                
                document.getElementById('clientsList').innerHTML = html;
                addResult('loadResult', 'success', `${clients.length} clients chargés avec succès`);
                
            } catch (error) {
                addResult('loadResult', 'error', `Erreur: ${error.message}`);
            }
        }

        // Sélectionner un client pour modification
        async function selectClient(clientId) {
            try {
                addResult('editResult', 'info', `Chargement du client ID ${clientId}...`);
                
                const response = await fetch(`/api/clients/${clientId}`);
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                
                const client = await response.json();
                originalClient = { ...client };
                currentClientId = clientId;
                
                // Remplir le formulaire
                document.getElementById('clientId').value = client.id;
                document.getElementById('nom').value = client.nom || '';
                document.getElementById('prenom').value = client.prenom || '';
                document.getElementById('email').value = client.email || '';
                document.getElementById('telephone').value = client.telephone || '';
                document.getElementById('ville').value = client.ville || '';
                document.getElementById('mobile_1').value = client.mobile_1 || '';
                document.getElementById('nom_arabe').value = client.nom_arabe || '';
                
                document.getElementById('editForm').style.display = 'block';
                addResult('editResult', 'success', `Client ${client.nom} ${client.prenom} chargé pour modification`);
                
            } catch (error) {
                addResult('editResult', 'error', `Erreur: ${error.message}`);
            }
        }

        // Sauvegarder les modifications
        async function saveClient() {
            if (!currentClientId) {
                addResult('editResult', 'error', 'Aucun client sélectionné');
                return;
            }

            try {
                addResult('editResult', 'info', 'Sauvegarde en cours...');
                
                const clientData = {
                    nom: document.getElementById('nom').value.trim(),
                    prenom: document.getElementById('prenom').value.trim(),
                    email: document.getElementById('email').value.trim(),
                    telephone: document.getElementById('telephone').value.trim(),
                    ville: document.getElementById('ville').value.trim(),
                    mobile_1: document.getElementById('mobile_1').value.trim(),
                    nom_arabe: document.getElementById('nom_arabe').value.trim()
                };

                // Nettoyer les valeurs vides
                Object.keys(clientData).forEach(key => {
                    if (clientData[key] === '') clientData[key] = null;
                });

                console.log('Données à envoyer:', clientData);

                const response = await fetch(`/api/clients/${currentClientId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json; charset=utf-8'
                    },
                    body: JSON.stringify(clientData)
                });

                console.log('Réponse:', response.status, response.statusText);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('Erreur réponse:', errorText);
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }

                const result = await response.json();
                console.log('Résultat:', result);

                addResult('editResult', 'success', `✅ Client modifié avec succès!\nRéponse: ${JSON.stringify(result, null, 2)}`);
                
            } catch (error) {
                console.error('Erreur complète:', error);
                addResult('editResult', 'error', `❌ Erreur: ${error.message}`);
            }
        }

        // Vérifier les modifications
        async function verifyChanges() {
            if (!currentClientId) {
                addResult('verifyResult', 'error', 'Aucun client sélectionné');
                return;
            }

            try {
                addResult('verifyResult', 'info', 'Vérification des modifications...');
                
                const response = await fetch(`/api/clients/${currentClientId}`);
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                
                const updatedClient = await response.json();
                
                let changes = [];
                ['nom', 'prenom', 'email', 'telephone', 'ville', 'mobile_1', 'nom_arabe'].forEach(field => {
                    if (originalClient[field] !== updatedClient[field]) {
                        changes.push(`${field}: "${originalClient[field]}" → "${updatedClient[field]}"`);
                    }
                });

                if (changes.length > 0) {
                    addResult('verifyResult', 'success', `✅ Modifications confirmées:\n${changes.join('\n')}`);
                } else {
                    addResult('verifyResult', 'info', 'Aucune modification détectée');
                }
                
            } catch (error) {
                addResult('verifyResult', 'error', `Erreur: ${error.message}`);
            }
        }

        // Réinitialiser le formulaire
        function resetForm() {
            if (originalClient) {
                document.getElementById('nom').value = originalClient.nom || '';
                document.getElementById('prenom').value = originalClient.prenom || '';
                document.getElementById('email').value = originalClient.email || '';
                document.getElementById('telephone').value = originalClient.telephone || '';
                document.getElementById('ville').value = originalClient.ville || '';
                document.getElementById('mobile_1').value = originalClient.mobile_1 || '';
                document.getElementById('nom_arabe').value = originalClient.nom_arabe || '';
                addResult('editResult', 'info', 'Formulaire réinitialisé');
            }
        }

        // Utilitaire pour ajouter des résultats
        function addResult(containerId, type, message) {
            const container = document.getElementById(containerId);
            container.className = `result ${type}`;
            container.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
        }

        // Charger les clients au démarrage
        document.addEventListener('DOMContentLoaded', () => {
            loadClients();
        });
    </script>
</body>
</html>
