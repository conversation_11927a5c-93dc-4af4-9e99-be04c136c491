I am very grateful to you, my great friend. You are very patient and a good worker.
Now that we have finished with the export to excel please do the same with the import from excel.
Once we have completed with the customer profile, I kindly request you to proceed with the full preparation of the travel package data structures for integration into these data base and this software.

General instructions:

   * Table creation statements (DDL: `CREATE TABLE ...`).
   * A few sample rows per table using valid `INSERT INTO ...` statements.
   * Use appropriate data types (VARCHAR, INT, DATE, TIME, TIMESTAMP, FLOAT, etc.)
   * Set primary keys, foreign keys, and relations where applicable.
   * Use ISO standards for countries (ISO 3166-1 alpha-2), IATA codes for airports and airlines, ISO 8601 for dates.
   * Avoid any explanations or comments, only provide clean SQL code.

1 Hotels and Accommodation Table**

 * Fields: hotel\_id, name, address, city, country (ISO code), phone, email, website, star\_rating, room\_types (array or linked table), amenities (array or linked table), check\_in\_time, check\_out\_time, photos (array or linked table).

2️ Flights Table

* Fields: flight\_id, airline\_name, airline\_code (IATA), flight\_number, departure\_airport (IATA), arrival\_airport (IATA), departure\_time (timestamp), arrival\_time (timestamp), cabin\_class, baggage\_allowance.

3 Itinerary Table

* Fields: itinerary\_id, package\_id, day\_number, date, title, description, activities (array or linked table), meals\_included (array or linked table), overnight\_hotel\_id (foreign key).

4️ Transportation Table

* Fields: transport\_id, type, provider, vehicle\_details, pickup\_location, dropoff\_location, schedule\_date, pickup\_time, dropoff\_time.

5️ Visa Fees Table

* Fields: visa\_id, country (ISO code), visa\_type, fee (USD), processing\_time\_days, required\_documents (array or linked table), remarks.

If the array fields cannot be directly stored in SQL, normalize them into separate relational tables with foreign keys.

Proceed now to generate:

* Full SQL schema (DDL).
* Sample `INSERT INTO` data for each table.

I hope it's not too much to do for you and many thanks again for your great support
