const XLSX = require('xlsx');
const path = require('path');

// Créer des données de test pour l'import Excel
const testData = [
    // Headers (ligne 1)
    [
        'Code Client', 'Nom', 'Prénom', 'Email', 'Téléphone', 'Mobile 1', 'Mobile 2', 
        'Adresse', 'Ville', 'Code Postal', 'CIN N°', 'Passeport N°', 
        'Date Expiration Passeport', 'Date de Naissance', 'Nationalité ID', 'Sexe ID', 
        'Situation Familiale ID', 'Pays de Naissance ID', 'Lieu de Naissance ID', 
        'Compte Comptable ID', 'Label Tél ID', 'Label Mobile 1 ID', 'Label Mobile 2 ID', 
        'Nom Arabe', 'Prénom Arabe', 'N° Carte Fidélité', 'Compagnie Aérienne ID', 
        'Nom Personne Liée', 'Label Personne Liée ID', 'Tél Personne Liée', 'Photo URL', 
        'Date Création', 'Date Modification'
    ],
    // Données de test (lignes 2-5) avec emails uniques garantis
    [
        '', 'DUPONT', '<PERSON>', `jean.dupont.${Date.now()}@test-import.com`, '0123456789', '0612345678', '',
        '123 Rue de la Paix', 'Paris', '75001', 'AB123456', 'FR1234567',
        '2030-12-31', '1985-06-15', '2', '1',
        '2', '2', '6',
        '1', '1', '1', '',
        'دوبون', 'جان', 'AF123456789', '2',
        'DUPONT Marie', '7', '+33612345678', '',
        '', ''
    ],
    [
        '', 'MARTIN', 'Sophie', `sophie.martin.${Date.now() + 1}@test-import.com`, '0234567890', '0623456789', '',
        '456 Avenue des Champs', 'Lyon', '69001', 'CD789012', 'FR7890123',
        '2029-08-20', '1990-03-22', '2', '2',
        '1', '2', '7',
        '2', '3', '1', '',
        'مارتن', 'صوفي', 'AF987654321', '2',
        'MARTIN Paul', '8', '+33623456789', '',
        '', ''
    ],
    [
        '', 'ALAOUI', 'Ahmed', `ahmed.alaoui.${Date.now() + 2}@test-import.com`, '0661234567', '0677889900', '',
        'Quartier Gueliz, Rue Yougoslavie', 'Marrakech', '40000', 'MA123456', 'MA7654321',
        '2031-05-10', '1988-11-08', '1', '1',
        '2', '1', '3',
        '3', '2', '1', '',
        'العلوي', 'أحمد', 'RAM55667788', '1',
        'ALAOUI Fatima', '7', '+212677889900', '',
        '', ''
    ],
    [
        '', 'GARCIA', 'Carlos', `carlos.garcia.${Date.now() + 3}@test-import.com`, '0034912345678', '0034687654321', '',
        'Calle Mayor 45', 'Madrid', '28001', 'ES123456', 'ES7890123',
        '2028-12-15', '1992-07-30', '3', '1',
        '1', '3', '9',
        '2', '1', '1', '',
        'غارسيا', 'كارلوس', 'IB987654321', '3',
        'GARCIA Maria', '7', '+34687654321', '',
        '', ''
    ]
];

// Créer le workbook
const workbook = XLSX.utils.book_new();
const worksheet = XLSX.utils.aoa_to_sheet(testData);

// Ajuster la largeur des colonnes
const columnWidths = [
    { wch: 12 }, { wch: 15 }, { wch: 15 }, { wch: 25 }, { wch: 15 },
    { wch: 15 }, { wch: 15 }, { wch: 30 }, { wch: 15 }, { wch: 10 },
    { wch: 12 }, { wch: 12 }, { wch: 12 }, { wch: 12 }, { wch: 10 },
    { wch: 8 }, { wch: 12 }, { wch: 12 }, { wch: 12 }, { wch: 12 },
    { wch: 10 }, { wch: 12 }, { wch: 12 }, { wch: 15 }, { wch: 15 },
    { wch: 15 }, { wch: 12 }, { wch: 20 }, { wch: 12 }, { wch: 15 },
    { wch: 30 }, { wch: 18 }, { wch: 18 }
];
worksheet['!cols'] = columnWidths;

// Ajouter la feuille au workbook
XLSX.utils.book_append_sheet(workbook, worksheet, 'Clients');

// Sauvegarder le fichier
const fileName = 'test_import_clients.xlsx';
const filePath = path.join(__dirname, 'frontend', fileName);

XLSX.writeFile(workbook, filePath);

console.log(`✅ Fichier Excel de test créé: ${filePath}`);
console.log(`📊 Contient ${testData.length - 1} clients de test`);
console.log(`🎯 Utilisez ce fichier pour tester l'import Excel`);
