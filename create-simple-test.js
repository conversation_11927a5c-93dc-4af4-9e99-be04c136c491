const XLSX = require('xlsx');
const path = require('path');

// <PERSON><PERSON>er un fichier Excel très simple pour test final
const timestamp = Date.now();

const testData = [
    // Headers
    ['Code Client', 'Nom', 'Prénom', 'Email', 'Téléphone', 'Mobile 1', 'Mobile 2', 'Adresse', 'Ville', 'Code Postal'],
    // Données avec emails garantis uniques
    ['', 'NOUVEAU', 'Client1', `nouveau.client1.${timestamp}@test-final.com`, '0123456789', '', '', 'Adresse Test 1', 'Paris', '75001'],
    ['', 'NOUVEAU', 'Client2', `nouveau.client2.${timestamp}@test-final.com`, '0234567890', '', '', 'Adresse Test 2', 'Lyon', '69001'],
    ['', 'NOUVEAU', 'Client3', `nouveau.client3.${timestamp}@test-final.com`, '0345678901', '', '', 'Adresse Test 3', 'Marseille', '13001']
];

// Créer le workbook
const workbook = XLSX.utils.book_new();
const worksheet = XLSX.utils.aoa_to_sheet(testData);

// Ajuster la largeur des colonnes
worksheet['!cols'] = [
    { wch: 12 }, { wch: 15 }, { wch: 15 }, { wch: 35 }, { wch: 15 },
    { wch: 15 }, { wch: 15 }, { wch: 25 }, { wch: 15 }, { wch: 10 }
];

// Ajouter la feuille au workbook
XLSX.utils.book_append_sheet(workbook, worksheet, 'Clients');

// Sauvegarder le fichier
const fileName = 'test_final_simple.xlsx';
const filePath = path.join(__dirname, 'frontend', fileName);

XLSX.writeFile(workbook, filePath);

console.log(`✅ Fichier Excel simple créé: ${filePath}`);
console.log(`📊 Contient ${testData.length - 1} nouveaux clients`);
console.log(`📧 Emails uniques avec timestamp: ${timestamp}`);
console.log(`🎯 Prêt pour test final de l'import Excel !`);

// Afficher le contenu pour vérification
console.log('\n📋 Contenu du fichier:');
testData.forEach((row, index) => {
    if (index === 0) {
        console.log('Headers:', row.join(' | '));
    } else {
        console.log(`Client ${index}:`, row[1], row[2], '-', row[3]);
    }
});
