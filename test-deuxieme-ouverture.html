<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Test Deuxième Ouverture - Onglet Documents</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f0f0f0; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        .result { margin: 15px 0; padding: 20px; border-radius: 8px; font-family: monospace; white-space: pre-wrap; }
        .success { background: #d4edda; color: #155724; border: 2px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 2px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 2px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 2px solid #ffeaa7; }
        button { padding: 15px 25px; margin: 10px; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; font-size: 16px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        h1 { color: #333; text-align: center; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #f9f9f9; }
        .step { background: #e9ecef; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }
        .step-number { background: #007bff; color: white; border-radius: 50%; width: 30px; height: 30px; display: inline-flex; align-items: center; justify-content: center; margin-right: 10px; font-weight: bold; }
        .critical { background: #dc3545; color: white; padding: 20px; border-radius: 8px; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 TEST DEUXIÈME OUVERTURE - CORRECTION APPLIQUÉE</h1>
        
        <div class="test-section">
            <h3>🚨 Problème Identifié et Corrigé</h3>
            <div class="critical">
⚠️ PROBLÈME EXACT: Onglet Documents vide à la deuxième ouverture

🔍 CAUSE IDENTIFIÉE:
- Les éléments HTML existent mais ne s'affichent pas
- L'onglet Documents n'est pas forcé visible
- Les éléments parents peuvent être cachés

✅ CORRECTIONS APPLIQUÉES:
1. Diagnostic DOM complet dans setupDocumentsTab()
2. Forçage de la visibilité de l'onglet Documents
3. Forçage de la visibilité de tous les éléments parents
4. Double tentative d'initialisation (300ms + 500ms)
5. Logs détaillés pour diagnostic
            </div>
        </div>

        <div class="test-section">
            <h3>1. Test Critique - Deuxième Ouverture</h3>
            
            <div class="step">
                <span class="step-number">1</span>
                <strong>Ouvrir Interface</strong>
                <button class="btn-primary" onclick="window.open('/', '_blank')">
                    🖥️ Ouvrir Interface Principale
                </button>
            </div>

            <div class="step">
                <span class="step-number">2</span>
                <strong>Test de Reproduction du Bug</strong>
                <div class="warning result">
📋 PROCÉDURE EXACTE POUR REPRODUIRE LE BUG:

1. Ouvrez l'interface principale
2. Cliquez sur "Modifier" pour un client (ex: HABIBI Karim)
3. Cliquez sur l'onglet "Documents"
4. VÉRIFIEZ: Interface complète (zone drag & drop, bouton, etc.)
5. Cliquez sur "Annuler" pour fermer le modal
6. IMMÉDIATEMENT: Cliquez à nouveau sur "Modifier" pour le même client
7. Cliquez sur l'onglet "Documents"
8. VÉRIFIEZ: Interface doit être complète (PAS VIDE comme avant)

RÉSULTAT ATTENDU MAINTENANT:
✅ Interface COMPLÈTE à la première ouverture
✅ Interface COMPLÈTE à la deuxième ouverture
✅ Interface COMPLÈTE à toutes les ouvertures suivantes
                </div>
            </div>

            <div class="step">
                <span class="step-number">3</span>
                <strong>Logs de Diagnostic (F12 → Console)</strong>
                <div class="info result">
📋 NOUVEAUX LOGS À VÉRIFIER:

À chaque ouverture de l'onglet Documents:
✅ "🔧 Initialisation onglet Documents..."
✅ "✅ Onglet Documents forcé visible"
✅ "🔄 Tentative d'initialisation Documents..."
✅ "🔧 Configuration onglet Documents..."
✅ "🔍 Diagnostic DOM complet: {tab-documents: true, ...}"
✅ "🔧 Forçage de la visibilité des éléments..."
✅ "✅ Setup Documents réussi, chargement des documents..."

SI ÉCHEC À LA PREMIÈRE TENTATIVE:
⚠️ "❌ Échec setup Documents - Nouvelle tentative..."
⚠️ "🔄 Deuxième tentative d'initialisation..."
✅ "✅ Setup Documents réussi à la deuxième tentative"

Ces logs confirment que la correction fonctionne !
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>2. Corrections Techniques Appliquées</h3>
            <div class="success result">
📋 CORRECTIONS DÉTAILLÉES:

1. FORÇAGE VISIBILITÉ ONGLET:
   - tabDocuments.style.display = 'block'
   - tabDocuments.classList.add('active')
   - Appliqué AVANT l'initialisation

2. DIAGNOSTIC DOM COMPLET:
   - Vérification de tous les éléments parents
   - Logs détaillés pour identifier les problèmes
   - Forçage de la visibilité de tous les parents

3. DOUBLE TENTATIVE:
   - Première tentative à 300ms
   - Deuxième tentative à 500ms si échec
   - Garantit l'initialisation même en cas de timing

4. FORÇAGE ÉLÉMENTS:
   - uploadZone, uploadDocumentBtn, documentType
   - Tous les éléments parents jusqu'au body
   - Suppression de tous les display: none
            </div>
        </div>

        <div class="test-section">
            <h3>3. Test Automatique</h3>
            <button class="btn-success" onclick="runAutomaticTest()">🧪 Test Automatique</button>
            <div id="testResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. Si Le Problème Persiste</h3>
            <div class="error result">
🚨 ACTIONS SUPPLÉMENTAIRES:

1. HARD REFRESH:
   - Ctrl+F5 pour vider complètement le cache
   - Ou F12 → Network → Disable cache + refresh

2. VÉRIFIEZ LES ERREURS JAVASCRIPT:
   - F12 → Console
   - Cherchez les erreurs en rouge
   - Notez les erreurs exactes

3. TESTEZ DANS UN AUTRE NAVIGATEUR:
   - Chrome, Firefox, Edge
   - Mode incognito/privé

4. VÉRIFIEZ LE DOM:
   - F12 → Elements
   - Cherchez id="tab-documents"
   - Vérifiez que le contenu HTML est présent

5. DONNEZ-MOI LES LOGS EXACTS:
   - Copiez tous les logs de la console
   - Indiquez à quelle étape ça échoue
            </div>
        </div>

        <div class="test-section">
            <h3>5. Code des Corrections</h3>
            <div class="info result">
📋 NOUVELLES FONCTIONS:

// Dans switchTab()
if (tabName === 'documents' && isEditing && currentClientId) {
    // Forcer l'affichage de l'onglet Documents AVANT l'initialisation
    const tabDocuments = document.getElementById('tab-documents');
    if (tabDocuments) {
        tabDocuments.style.display = 'block';
        tabDocuments.classList.add('active');
    }
    
    // Double tentative d'initialisation
    setTimeout(() => {
        const success = setupDocumentsTab();
        if (!success) {
            // Deuxième tentative après 500ms
            setTimeout(() => setupDocumentsTab(), 500);
        }
    }, 300);
}

// Dans setupDocumentsTab()
// Diagnostic DOM complet + forçage visibilité parents
            </div>
        </div>
    </div>

    <script>
        function runAutomaticTest() {
            const result = document.getElementById('testResult');
            result.className = 'result info';
            result.textContent = 'Test automatique en cours...';
            
            setTimeout(() => {
                // Ouvrir l'interface dans une nouvelle fenêtre
                const testWindow = window.open('/', '_blank');
                
                setTimeout(() => {
                    result.className = 'result success';
                    result.textContent = `✅ Test automatique lancé !

Instructions pour la nouvelle fenêtre:
1. Modifiez un client → Onglet Documents (1ère fois)
2. Fermez le modal
3. Modifiez le même client → Onglet Documents (2ème fois)
4. Vérifiez que l'interface est COMPLÈTE les deux fois

Regardez les logs dans la console (F12) pour confirmer que les corrections fonctionnent.

Si vous voyez tous les logs mentionnés ci-dessus, le problème est résolu !`;
                }, 1000);
                
            }, 500);
        }

        // Auto-start
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🔧 Page de test deuxième ouverture chargée');
            const result = document.getElementById('testResult');
            result.className = 'result info';
            result.textContent = 'Page de test prête. Cliquez sur "Test Automatique" pour lancer le test.';
        });
    </script>
</body>
</html>
