const XLSX = require('xlsx');
const path = require('path');

// Créer un fichier Excel avec des emails intentionnellement en double
// pour tester la correction automatique

const testData = [
    // Headers
    [
        'Code Client', 'Nom', 'Prénom', 'Email', 'Téléphone', 'Mobile 1', 'Mobile 2', 
        'Adresse', 'Ville', 'Code Postal', 'CIN N°', 'Passeport N°', 
        'Date Expiration Passeport', 'Date de Naissance', 'Nationalité ID', 'Sexe ID', 
        'Situation Familiale ID', 'Pays de Naissance ID', 'Lieu de Naissance ID', 
        'Compte Comptable ID', 'Label Tél ID', 'Label Mobile 1 ID', 'Label Mobile 2 ID', 
        'Nom Arabe', 'Prénom Arabe', 'N° Carte Fidélité', 'Compagnie Aérienne ID', 
        'Nom Personne Liée', 'Label Personne Liée ID', 'Tél Personne Liée', 'Photo URL', 
        'Date Création', 'Date Modification'
    ],
    // Client 1 - <PERSON>ail qui va être en conflit avec un email existant
    [
        '', 'DUPLICATE', 'Test1', '<EMAIL>', '0123456789', '', '',
        'Adresse Test 1', 'Paris', '75001', '', '', 
        '', '', '', '1', 
        '', '', '', 
        '', '', '', '', 
        '', '', '', '', 
        '', '', '', '', 
        '', ''
    ],
    // Client 2 - Email unique qui devrait passer
    [
        '', 'UNIQUE', 'Test2', `unique.test.${Date.now()}@test-correction.com`, '0234567890', '', '',
        'Adresse Test 2', 'Lyon', '69001', '', '', 
        '', '', '', '2', 
        '', '', '', 
        '', '', '', '', 
        '', '', '', '', 
        '', '', '', '', 
        '', ''
    ],
    // Client 3 - Même email que Client 1 pour tester la correction
    [
        '', 'DUPLICATE', 'Test3', '<EMAIL>', '0345678901', '', '',
        'Adresse Test 3', 'Marseille', '13001', '', '', 
        '', '', '', '1', 
        '', '', '', 
        '', '', '', '', 
        '', '', '', '', 
        '', '', '', '', 
        '', ''
    ]
];

// Créer le workbook
const workbook = XLSX.utils.book_new();
const worksheet = XLSX.utils.aoa_to_sheet(testData);

// Ajuster la largeur des colonnes
const columnWidths = [
    { wch: 12 }, { wch: 15 }, { wch: 15 }, { wch: 35 }, { wch: 15 },
    { wch: 15 }, { wch: 15 }, { wch: 25 }, { wch: 15 }, { wch: 10 },
    { wch: 12 }, { wch: 12 }, { wch: 12 }, { wch: 12 }, { wch: 10 },
    { wch: 8 }, { wch: 12 }, { wch: 12 }, { wch: 12 }, { wch: 12 },
    { wch: 10 }, { wch: 12 }, { wch: 12 }, { wch: 15 }, { wch: 15 },
    { wch: 15 }, { wch: 12 }, { wch: 20 }, { wch: 12 }, { wch: 15 },
    { wch: 30 }, { wch: 18 }, { wch: 18 }
];
worksheet['!cols'] = columnWidths;

// Ajouter la feuille au workbook
XLSX.utils.book_append_sheet(workbook, worksheet, 'Clients');

// Sauvegarder le fichier
const fileName = 'test_duplicate_emails.xlsx';
const filePath = path.join(__dirname, 'frontend', fileName);

XLSX.writeFile(workbook, filePath);

console.log(`✅ Fichier Excel de test créé: ${filePath}`);
console.log(`📊 Contient ${testData.length - 1} clients de test`);
console.log(`📧 EMAILS DE TEST:`);
console.log(`   - Client 1: <EMAIL> (CONFLIT ATTENDU)`);
console.log(`   - Client 2: unique.test.${Date.now()}@test-correction.com (UNIQUE)`);
console.log(`   - Client 3: <EMAIL> (CONFLIT ATTENDU)`);
console.log(`🎯 Parfait pour tester la correction automatique des emails !`);
console.log(`\n💡 RÉSULTAT ATTENDU:`);
console.log(`   ✅ Client 1: Email modifié automatiquement`);
console.log(`   ✅ Client 2: Import normal`);
console.log(`   ✅ Client 3: Email modifié automatiquement`);
console.log(`   📊 Total: 3 clients importés avec succès`);
