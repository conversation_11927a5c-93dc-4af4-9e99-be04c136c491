// Test simple de modification via Node.js
const fetch = require('node-fetch');

async function testModification() {
    try {
        console.log('🧪 Test de modification client...');
        
        // 1. Récupérer le client existant
        console.log('📋 Récupération du client ID 1...');
        const getResponse = await fetch('http://localhost:3000/api/clients/1');
        const originalClient = await getResponse.json();
        console.log('✅ Client original:', originalClient.nom, originalClient.prenom);
        
        // 2. Préparer les données de modification
        const modificationData = {
            nom: 'HABIBI_MODIFIE_TEST',
            prenom: 'Karim_Test',
            email: originalClient.email, // Garder l'email original
            telephone: originalClient.telephone,
            ville: 'Casablanca_Test_Modifiee',
            mobile_1: '0612345678',
            nom_arabe: 'حبيبي تست'
        };
        
        console.log('📝 Données de modification:', modificationData);
        
        // 3. Envoyer la modification
        console.log('🔄 Envoi de la modification...');
        const putResponse = await fetch('http://localhost:3000/api/clients/1', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json; charset=utf-8'
            },
            body: JSON.stringify(modificationData)
        });
        
        console.log('📥 Statut réponse:', putResponse.status, putResponse.statusText);
        
        if (!putResponse.ok) {
            const errorText = await putResponse.text();
            console.error('❌ Erreur:', errorText);
            return;
        }
        
        const result = await putResponse.json();
        console.log('✅ Résultat modification:', result);
        
        // 4. Vérifier la modification
        console.log('🔍 Vérification de la modification...');
        const verifyResponse = await fetch('http://localhost:3000/api/clients/1');
        const modifiedClient = await verifyResponse.json();
        
        console.log('📊 Comparaison:');
        console.log('  Nom:', originalClient.nom, '→', modifiedClient.nom);
        console.log('  Prénom:', originalClient.prenom, '→', modifiedClient.prenom);
        console.log('  Ville:', originalClient.ville, '→', modifiedClient.ville);
        console.log('  Mobile 1:', originalClient.mobile_1, '→', modifiedClient.mobile_1);
        console.log('  Nom Arabe:', originalClient.nom_arabe, '→', modifiedClient.nom_arabe);
        
        if (modifiedClient.nom === modificationData.nom) {
            console.log('🎉 SUCCÈS: La modification a été enregistrée !');
        } else {
            console.log('❌ ÉCHEC: La modification n\'a pas été enregistrée');
        }
        
    } catch (error) {
        console.error('❌ Erreur test:', error.message);
    }
}

// Lancer le test
testModification();
