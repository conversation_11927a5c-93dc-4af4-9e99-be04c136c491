<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Clients</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🏢 Système de Gestion des Clients</h1>
            <p>Gérez facilement votre base de données clients</p>
        </header>

        <!-- Barre de recherche -->
        <div class="search-section">
            <div class="search-container">
                <input type="text" id="searchInput" placeholder="Rechercher un client (nom, prénom, email, ville...)">
                <button id="searchBtn">🔍 Rechercher</button>
                <button id="clearSearchBtn">❌ Effacer</button>
            </div>
        </div>

        <!-- Bouton pour ajouter un client -->
        <div class="actions">
            <button id="addClientBtn" class="btn-primary">➕ Ajouter un Client</button>
        </div>

        <!-- Liste des clients -->
        <div class="clients-section">
            <h2>Liste des Clients</h2>
            <div id="clientsContainer">
                <div class="loading">Chargement des clients...</div>
            </div>
        </div>

        <!-- Modal pour ajouter/modifier un client -->
        <div id="clientModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="modalTitle">Ajouter un Client</h3>
                    <span class="close">&times;</span>
                </div>
                <form id="clientForm">
                    <!-- Section Photo -->
                    <div class="form-group photo-section">
                        <label>Photo de profil</label>
                        <div class="photo-upload-container">
                            <div class="photo-preview" id="photoPreview">
                                <img id="previewImage" src="" alt="Aperçu photo" style="display: none;">
                                <div class="photo-placeholder">📷 Aucune photo</div>
                            </div>
                            <input type="file" id="photoInput" accept="image/*" style="display: none;">
                            <button type="button" id="selectPhotoBtn" class="btn-secondary">📷 Choisir une photo</button>
                            <button type="button" id="removePhotoBtn" class="btn-danger" style="display: none;">❌ Supprimer</button>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="nom">Nom *</label>
                            <input type="text" id="nom" name="nom" required>
                        </div>
                        <div class="form-group">
                            <label for="prenom">Prénom *</label>
                            <input type="text" id="prenom" name="prenom" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="email">Email *</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="telephone">Téléphone</label>
                        <input type="tel" id="telephone" name="telephone">
                    </div>
                    
                    <div class="form-group">
                        <label for="adresse">Adresse</label>
                        <textarea id="adresse" name="adresse" rows="2"></textarea>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="ville">Ville</label>
                            <input type="text" id="ville" name="ville">
                        </div>
                        <div class="form-group">
                            <label for="code_postal">Code Postal</label>
                            <input type="text" id="code_postal" name="code_postal">
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn-primary">💾 Enregistrer</button>
                        <button type="button" class="btn-secondary" id="cancelBtn">❌ Annuler</button>
                    </div>
                </form>

                <!-- Section Documents (visible seulement en mode édition) -->
                <div id="documentsSection" style="display: none;">
                    <hr>
                    <h4>📄 Documents du Client</h4>

                    <div class="document-upload">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="documentType">Type de document</label>
                                <select id="documentType">
                                    <option value="passeport">🛂 Passeport</option>
                                    <option value="carte_identite">🆔 Carte d'identité</option>
                                    <option value="permis_conduire">🚗 Permis de conduire</option>
                                    <option value="autre">📄 Autre document</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <input type="file" id="documentInput" accept=".pdf,.jpg,.jpeg,.png,.gif">
                                <button type="button" id="uploadDocumentBtn" class="btn-primary">📤 Ajouter Document</button>
                            </div>
                        </div>
                    </div>

                    <div id="documentsContainer">
                        <div class="loading">Chargement des documents...</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal de confirmation de suppression -->
        <div id="deleteModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Confirmer la suppression</h3>
                    <span class="close">&times;</span>
                </div>
                <p>Êtes-vous sûr de vouloir supprimer ce client ?</p>
                <div class="form-actions">
                    <button id="confirmDeleteBtn" class="btn-danger">🗑️ Supprimer</button>
                    <button id="cancelDeleteBtn" class="btn-secondary">❌ Annuler</button>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
