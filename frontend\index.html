<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>✈️ Gestion des Clients - Agence de Voyage</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="modal.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation moderne -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-plane"></i>
                <span>TravelCRM</span>
            </div>
            <div class="nav-menu">
                <a href="#" class="nav-link active"><i class="fas fa-users"></i> Clients</a>
                <a href="#" class="nav-link"><i class="fas fa-chart-bar"></i> Statistiques</a>
                <a href="#" class="nav-link"><i class="fas fa-cog"></i> Paramètres</a>
            </div>
        </div>
    </nav>

    <div class="container">
        <!-- Header moderne avec gradient -->
        <header class="modern-header">
            <div class="header-content">
                <div class="header-text">
                    <h1><i class="fas fa-users-cog"></i> Gestion des Clients</h1>
                    <p>Système complet de gestion clientèle pour agence de voyage</p>
                </div>
                <div class="header-stats">
                    <div class="stat-card">
                        <div class="stat-number" id="totalClients">0</div>
                        <div class="stat-label">Clients Total</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="newClientsMonth">0</div>
                        <div class="stat-label">Ce Mois</div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Barre de recherche moderne -->
        <div class="search-section">
            <div class="search-container">
                <div class="search-input-group">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" id="searchInput" placeholder="Rechercher par nom, prénom, email, code client...">
                    <button id="clearSearchBtn" class="clear-btn"><i class="fas fa-times"></i></button>
                </div>
                <div class="search-filters">
                    <select id="filterNationality" class="filter-select">
                        <option value="">Toutes nationalités</option>
                    </select>
                    <select id="filterSexe" class="filter-select">
                        <option value="">Tous sexes</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Actions avec boutons modernes -->
        <div class="actions-bar">
            <button id="addClientBtn" class="btn-primary">
                <i class="fas fa-user-plus"></i>
                <span>Nouveau Client</span>
            </button>
            <button id="exportBtn" class="btn-secondary">
                <i class="fas fa-download"></i>
                <span>Exporter</span>
            </button>
            <button id="importBtn" class="btn-secondary">
                <i class="fas fa-upload"></i>
                <span>Importer</span>
            </button>
        </div>

        <!-- Liste des clients -->
        <div class="clients-section">
            <h2>Liste des Clients</h2>
            <div id="clientsContainer">
                <div class="loading">Chargement des clients...</div>
            </div>
        </div>

        <!-- Modal moderne pour ajouter/modifier un client -->
        <div id="clientModal" class="modal">
            <div class="modal-content modern-modal" id="modalContent">
                <div class="modal-header" id="modalHeader">
                    <div class="modal-title-section">
                        <i class="fas fa-user-edit"></i>
                        <h3 id="modalTitle">Nouveau Client</h3>
                    </div>
                    <div class="header-controls">
                        <button class="btn-icon" id="minimizeBtn" title="Réduire">
                            <i class="fas fa-minus"></i>
                        </button>
                        <button class="btn-icon close" title="Fermer">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>

                <!-- Onglets de navigation -->
                <div class="tabs-container">
                    <div class="tabs-nav">
                        <button class="tab-btn active" data-tab="general">
                            <i class="fas fa-user"></i>
                            <span>Général</span>
                        </button>
                        <button class="tab-btn" data-tab="identity">
                            <i class="fas fa-id-card"></i>
                            <span>Identité</span>
                        </button>
                        <button class="tab-btn" data-tab="contact">
                            <i class="fas fa-phone"></i>
                            <span>Contact</span>
                        </button>
                        <button class="tab-btn" data-tab="travel">
                            <i class="fas fa-plane"></i>
                            <span>Voyage</span>
                        </button>
                        <button class="tab-btn" data-tab="documents">
                            <i class="fas fa-folder"></i>
                            <span>Documents</span>
                        </button>
                    </div>
                </div>
                <form id="clientForm">
                    <!-- Onglet Général -->
                    <div class="tab-content active" id="tab-general">
                        <div class="tab-header">
                            <h4><i class="fas fa-user"></i> Informations Générales</h4>
                            <p>Informations de base du client</p>
                        </div>

                        <!-- Section Photo -->
                        <div class="photo-section-modern">
                            <div class="photo-container">
                                <div class="photo-preview" id="photoPreview">
                                    <img id="previewImage" src="" alt="Photo client">
                                    <div class="photo-placeholder">
                                        <i class="fas fa-camera"></i>
                                        <span>Photo de profil</span>
                                    </div>
                                </div>
                                <div class="photo-actions">
                                    <input type="file" id="photoInput" accept="image/*" style="display: none;">
                                    <button type="button" id="selectPhotoBtn" class="btn-photo">
                                        <i class="fas fa-camera"></i> Choisir
                                    </button>
                                    <button type="button" id="removePhotoBtn" class="btn-photo-remove" style="display: none;">
                                        <i class="fas fa-trash"></i> Supprimer
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="form-grid">
                            <div class="form-group">
                                <label for="nom"><i class="fas fa-user"></i> Nom *</label>
                                <input type="text" id="nom" name="nom" required placeholder="Nom de famille">
                            </div>
                            <div class="form-group">
                                <label for="prenom"><i class="fas fa-user"></i> Prénom *</label>
                                <input type="text" id="prenom" name="prenom" required placeholder="Prénom">
                            </div>
                            <div class="form-group">
                                <label for="nom_arabe"><i class="fas fa-language"></i> Nom en Arabe</label>
                                <input type="text" id="nom_arabe" name="nom_arabe" placeholder="الاسم العائلي" dir="rtl">
                            </div>
                            <div class="form-group">
                                <label for="prenom_arabe"><i class="fas fa-language"></i> Prénom en Arabe</label>
                                <input type="text" id="prenom_arabe" name="prenom_arabe" placeholder="الاسم الشخصي" dir="rtl">
                            </div>
                            <div class="form-group full-width">
                                <label for="email"><i class="fas fa-envelope"></i> Email *</label>
                                <input type="email" id="email" name="email" required placeholder="<EMAIL>">
                            </div>
                        </div>

                        <div class="form-grid">
                            <div class="form-group">
                                <label for="sexe_id"><i class="fas fa-venus-mars"></i> Sexe</label>
                                <select id="sexe_id" name="sexe_id">
                                    <option value="">Sélectionner...</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="date_de_naissance"><i class="fas fa-birthday-cake"></i> Date de Naissance</label>
                                <input type="date" id="date_de_naissance" name="date_de_naissance">
                            </div>
                            <div class="form-group">
                                <label for="nationality_id"><i class="fas fa-flag"></i> Nationalité</label>
                                <select id="nationality_id" name="nationality_id">
                                    <option value="">Sélectionner...</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="situation_familiale_id"><i class="fas fa-heart"></i> Situation Familiale</label>
                                <select id="situation_familiale_id" name="situation_familiale_id">
                                    <option value="">Sélectionner...</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Onglet Identité -->
                    <div class="tab-content" id="tab-identity">
                        <div class="tab-header">
                            <h4><i class="fas fa-id-card"></i> Documents d'Identité</h4>
                            <p>Informations officielles et documents</p>
                        </div>

                        <div class="form-grid">
                            <div class="form-group">
                                <label for="cin_no"><i class="fas fa-id-card"></i> N° Carte d'Identité</label>
                                <input type="text" id="cin_no" name="cin_no" placeholder="AB123456">
                            </div>
                            <div class="form-group">
                                <label for="passeport_no"><i class="fas fa-passport"></i> N° Passeport</label>
                                <input type="text" id="passeport_no" name="passeport_no" placeholder="M1234567">
                            </div>
                            <div class="form-group">
                                <label for="date_expiration_passeport"><i class="fas fa-calendar-times"></i> Expiration Passeport</label>
                                <input type="date" id="date_expiration_passeport" name="date_expiration_passeport">
                            </div>
                        </div>

                        <div class="form-grid">
                            <div class="form-group">
                                <label for="pays_de_naissance_id"><i class="fas fa-globe"></i> Pays de Naissance</label>
                                <select id="pays_de_naissance_id" name="pays_de_naissance_id">
                                    <option value="">Sélectionner...</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="lieu_de_naissance_id"><i class="fas fa-map-marker-alt"></i> Lieu de Naissance</label>
                                <select id="lieu_de_naissance_id" name="lieu_de_naissance_id">
                                    <option value="">Sélectionner...</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="compte_comptable_id"><i class="fas fa-calculator"></i> Compte Comptable</label>
                                <select id="compte_comptable_id" name="compte_comptable_id">
                                    <option value="">Sélectionner...</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Onglet Contact -->
                    <div class="tab-content" id="tab-contact">
                        <div class="tab-header">
                            <h4><i class="fas fa-phone"></i> Informations de Contact</h4>
                            <p>Coordonnées et adresses</p>
                        </div>

                        <div class="contact-section">
                            <h5><i class="fas fa-home"></i> Adresse Principale</h5>
                            <div class="form-grid">
                                <div class="form-group full-width">
                                    <label for="adresse"><i class="fas fa-map-marker-alt"></i> Adresse</label>
                                    <textarea id="adresse" name="adresse" rows="2" placeholder="Adresse complète"></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="ville"><i class="fas fa-city"></i> Ville</label>
                                    <input type="text" id="ville" name="ville" placeholder="Ville">
                                </div>
                                <div class="form-group">
                                    <label for="code_postal"><i class="fas fa-mail-bulk"></i> Code Postal</label>
                                    <input type="text" id="code_postal" name="code_postal" placeholder="12345">
                                </div>
                            </div>
                        </div>

                        <div class="contact-section">
                            <h5><i class="fas fa-phone"></i> Téléphones</h5>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="telephone"><i class="fas fa-phone"></i> Téléphone Principal</label>
                                    <input type="tel" id="telephone" name="telephone" placeholder="+212 5XX XX XX XX">
                                </div>
                                <div class="form-group">
                                    <label for="label_tel_id"><i class="fas fa-tag"></i> Type Téléphone</label>
                                    <select id="label_tel_id" name="label_tel_id">
                                        <option value="">Sélectionner...</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="mobile_1"><i class="fas fa-mobile-alt"></i> Mobile 1</label>
                                    <input type="tel" id="mobile_1" name="mobile_1" placeholder="+212 6XX XX XX XX">
                                </div>
                                <div class="form-group">
                                    <label for="label_mobile_1_id"><i class="fas fa-tag"></i> Type Mobile 1</label>
                                    <select id="label_mobile_1_id" name="label_mobile_1_id">
                                        <option value="">Sélectionner...</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="mobile_2"><i class="fas fa-mobile-alt"></i> Mobile 2</label>
                                    <input type="tel" id="mobile_2" name="mobile_2" placeholder="+212 6XX XX XX XX">
                                </div>
                                <div class="form-group">
                                    <label for="label_mobile_2_id"><i class="fas fa-tag"></i> Type Mobile 2</label>
                                    <select id="label_mobile_2_id" name="label_mobile_2_id">
                                        <option value="">Sélectionner...</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="contact-section">
                            <h5><i class="fas fa-user-friends"></i> Contact d'Urgence</h5>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="related_name"><i class="fas fa-user"></i> Nom du Contact</label>
                                    <input type="text" id="related_name" name="related_name" placeholder="Nom complet">
                                </div>
                                <div class="form-group">
                                    <label for="label_related_name_id"><i class="fas fa-heart"></i> Relation</label>
                                    <select id="label_related_name_id" name="label_related_name_id">
                                        <option value="">Sélectionner...</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="tel_related_name"><i class="fas fa-phone"></i> Téléphone Contact</label>
                                    <input type="tel" id="tel_related_name" name="tel_related_name" placeholder="+212 XXX XX XX XX">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Onglet Voyage -->
                    <div class="tab-content" id="tab-travel">
                        <div class="tab-header">
                            <h4><i class="fas fa-plane"></i> Informations Voyage</h4>
                            <p>Cartes de fidélité et préférences voyage</p>
                        </div>

                        <div class="travel-section">
                            <h5><i class="fas fa-star"></i> Programme de Fidélité</h5>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="airline_code_for_loyalty_card_id"><i class="fas fa-plane"></i> Compagnie Aérienne</label>
                                    <select id="airline_code_for_loyalty_card_id" name="airline_code_for_loyalty_card_id">
                                        <option value="">Sélectionner...</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="loyalty_card_no"><i class="fas fa-credit-card"></i> N° Carte Fidélité</label>
                                    <input type="text" id="loyalty_card_no" name="loyalty_card_no" placeholder="RAM123456789">
                                </div>
                            </div>
                        </div>

                        <div class="loyalty-preview" id="loyaltyPreview" style="display: none;">
                            <div class="loyalty-card">
                                <div class="card-header">
                                    <span class="airline-name" id="airlineName"></span>
                                    <span class="card-type">Loyalty Card</span>
                                </div>
                                <div class="card-number" id="cardNumber"></div>
                                <div class="card-holder" id="cardHolder"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Onglet Documents -->
                    <div class="tab-content" id="tab-documents">
                        <div class="tab-header">
                            <h4><i class="fas fa-folder"></i> Documents Client</h4>
                            <p>Gestion des documents et pièces jointes</p>
                        </div>

                        <div id="documentsSection" class="documents-section">
                            <div class="document-upload-area">
                                <div class="upload-zone" id="uploadZone">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    <h5>Glisser-déposer vos fichiers ici</h5>
                                    <p>ou cliquez pour sélectionner</p>
                                    <input type="file" id="documentInput" accept=".pdf,.jpg,.jpeg,.png,.gif" multiple style="display: none;">
                                </div>

                                <div class="upload-controls">
                                    <select id="documentType" class="document-type-select">
                                        <option value="passeport">🛂 Passeport</option>
                                        <option value="carte_identite">🆔 Carte d'identité</option>
                                        <option value="permis_conduire">🚗 Permis de conduire</option>
                                        <option value="autre">📄 Autre document</option>
                                    </select>
                                    <button type="button" id="uploadDocumentBtn" class="btn-upload">
                                        <i class="fas fa-upload"></i> Ajouter Document
                                    </button>
                                </div>
                            </div>

                            <div id="documentsContainer" class="documents-grid">
                                <!-- Les documents seront affichés ici -->
                            </div>
                        </div>
                    </div>

                    <!-- Barre d'actions moderne -->
                    <div class="modal-actions">
                        <div class="actions-left">
                            <button type="button" class="btn-outline" id="previewBtn">
                                <i class="fas fa-eye"></i> Aperçu
                            </button>
                        </div>
                        <div class="actions-right">
                            <button type="button" class="btn-secondary" id="cancelBtn">
                                <i class="fas fa-times"></i> Annuler
                            </button>
                            <button type="submit" class="btn-primary">
                                <i class="fas fa-save"></i> Enregistrer
                            </button>
                        </div>
                    </div>
                </form>


            </div>
        </div>

        <!-- Modal de confirmation de suppression -->
        <div id="deleteModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Confirmer la suppression</h3>
                    <span class="close">&times;</span>
                </div>
                <p>Êtes-vous sûr de vouloir supprimer ce client ?</p>
                <div class="form-actions">
                    <button id="confirmDeleteBtn" class="btn-danger">🗑️ Supprimer</button>
                    <button id="cancelDeleteBtn" class="btn-secondary">❌ Annuler</button>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
