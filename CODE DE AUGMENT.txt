CREATE TABLE nationality (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(2) NOT NULL UNIQUE,
    name_fr VARCHAR(100) NOT NULL,
    name_ar VARCHAR(100)
);

CREATE TABLE sexe (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name_fr VARCHAR(20) NOT NULL,
    name_ar VARCHAR(20)
);

CREATE TABLE situation_familiale (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name_fr VARCHAR(50) NOT NULL,
    name_ar VARCHAR(50)
);

CREATE TABLE pays (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(2) NOT NULL UNIQUE,
    name_fr VARCHAR(100) NOT NULL,
    name_ar VARCHAR(100)
);

CREATE TABLE ville (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name_fr VARCHAR(100) NOT NULL,
    name_ar VARCHAR(100),
    pays_id INT,
    FOREIGN KEY (pays_id) REFERENCES pays(id)
);

CREATE TABLE compte_comptable (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(20) NOT NULL UNIQUE,
    name_fr VARCHAR(100) NOT NULL,
    name_ar VARCHAR(100)
);

CREATE TABLE label_contact (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name_fr VARCHAR(50) NOT NULL,
    name_ar VARCHAR(50)
);

CREATE TABLE compagnie_aerienne (
    id INT PRIMARY KEY AUTO_INCREMENT,
    iata_code VARCHAR(2) NOT NULL UNIQUE,
    icao_code VARCHAR(3),
    name_fr VARCHAR(100) NOT NULL,
    name_ar VARCHAR(100)
);

CREATE TABLE clients (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code_client VARCHAR(20) NOT NULL UNIQUE,
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100) NOT NULL,
    nom_arabe VARCHAR(100),
    prenom_arabe VARCHAR(100),
    email VARCHAR(255),
    telephone VARCHAR(20),
    mobile_1 VARCHAR(20),
    mobile_2 VARCHAR(20),
    adresse TEXT,
    ville VARCHAR(100),
    code_postal VARCHAR(10),
    cin_no VARCHAR(50),
    passeport_no VARCHAR(50),
    date_expiration_passeport DATE,
    date_de_naissance DATE,
    nationality_id INT,
    sexe_id INT,
    situation_familiale_id INT,
    pays_de_naissance_id INT,
    lieu_de_naissance_id INT,
    compte_comptable_id INT,
    label_tel_id INT,
    label_mobile_1_id INT,
    label_mobile_2_id INT,
    related_name VARCHAR(100),
    label_related_name_id INT,
    tel_related_name VARCHAR(20),
    airline_code_for_loyalty_card_id INT,
    loyalty_card_no VARCHAR(50),
    photo_url VARCHAR(500),
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (nationality_id) REFERENCES nationality(id),
    FOREIGN KEY (sexe_id) REFERENCES sexe(id),
    FOREIGN KEY (situation_familiale_id) REFERENCES situation_familiale(id),
    FOREIGN KEY (pays_de_naissance_id) REFERENCES pays(id),
    FOREIGN KEY (lieu_de_naissance_id) REFERENCES ville(id),
    FOREIGN KEY (compte_comptable_id) REFERENCES compte_comptable(id),
    FOREIGN KEY (label_tel_id) REFERENCES label_contact(id),
    FOREIGN KEY (label_mobile_1_id) REFERENCES label_contact(id),
    FOREIGN KEY (label_mobile_2_id) REFERENCES label_contact(id),
    FOREIGN KEY (label_related_name_id) REFERENCES label_contact(id),
    FOREIGN KEY (airline_code_for_loyalty_card_id) REFERENCES compagnie_aerienne(id)
);

CREATE TABLE document_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name_fr VARCHAR(100) NOT NULL,
    name_ar VARCHAR(100)
);

CREATE TABLE client_documents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    client_id INT NOT NULL,
    document_type_id INT NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT,
    mime_type VARCHAR(100),
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    FOREIGN KEY (document_type_id) REFERENCES document_types(id)
);

CREATE TABLE voyages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    client_id INT NOT NULL,
    destination VARCHAR(100) NOT NULL,
    date_depart DATE NOT NULL,
    date_retour DATE,
    prix DECIMAL(10,2),
    statut VARCHAR(50),
    notes TEXT,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE
);

INSERT INTO nationality (code, name_fr, name_ar) VALUES
('MA', 'Marocaine', 'مغربية'),
('FR', 'Française', 'فرنسية'),
('ES', 'Espagnole', 'إسبانية'),
('DE', 'Allemande', 'ألمانية'),
('US', 'Américaine', 'أمريكية'),
('GB', 'Britannique', 'بريطانيا'),
('IT', 'Italienne', 'إيطالية'),
('CA', 'Canadienne', 'كندية');

INSERT INTO sexe (name_fr, name_ar) VALUES
('Masculin', 'ذكر'),
('Féminin', 'أنثى');

INSERT INTO situation_familiale (name_fr, name_ar) VALUES
('Célibataire', 'أعزب'),
('Marié(e)', 'متزوج'),
('Divorcé(e)', 'مطلق'),
('Veuf/Veuve', 'أرمل');

INSERT INTO pays (code, name_fr, name_ar) VALUES
('MA', 'Maroc', 'المغرب'),
('FR', 'France', 'فرنسا'),
('ES', 'Espagne', 'إسبانيا'),
('DE', 'Allemagne', 'ألمانيا'),
('US', 'États-Unis', 'الولايات المتحدة'),
('GB', 'Royaume-Uni', 'المملكة المتحدة'),
('IT', 'Italie', 'إيطاليا'),
('CA', 'Canada', 'كندا'),
('AE', 'Émirats Arabes Unis', 'الإمارات العربية المتحدة'),
('SA', 'Arabie Saoudite', 'المملكة العربية السعودية');

INSERT INTO ville (name_fr, name_ar, pays_id) VALUES
('Casablanca', 'الدار البيضاء', 1),
('Rabat', 'الرباط', 1),
('Marrakech', 'مراكش', 1),
('Fès', 'فاس', 1),
('Tanger', 'طنجة', 1),
('Paris', 'باريس', 2),
('Lyon', 'ليون', 2),
('Marseille', 'مرسيليا', 2),
('Madrid', 'مدريد', 3),
('Barcelona', 'برشلونة', 3);

INSERT INTO compte_comptable (code, name_fr, name_ar) VALUES
('411001', 'Clients Particuliers', 'عملاء أفراد'),
('411002', 'Clients Entreprises', 'عملاء شركات'),
('411003', 'Clients VIP', 'عملاء مميزون'),
('411004', 'Clients Groupes', 'عملاء مجموعات');

INSERT INTO label_contact (name_fr, name_ar) VALUES
('Personnel', 'شخصي'),
('Professionnel', 'مهني'),
('Domicile', 'المنزل'),
('Bureau', 'المكتب'),
('Mobile', 'محمول'),
('Urgence', 'طوارئ'),
('Famille', 'عائلة'),
('Ami', 'صديق');

INSERT INTO compagnie_aerienne (iata_code, icao_code, name_fr, name_ar) VALUES
('AT', 'RAM', 'Royal Air Maroc', 'الخطوط الملكية المغربية'),
('AF', 'AFR', 'Air France', 'الخطوط الجوية الفرنسية'),
('IB', 'IBE', 'Iberia', 'إيبيريا'),
('LH', 'DLH', 'Lufthansa', 'لوفتهانزا'),
('BA', 'BAW', 'British Airways', 'الخطوط الجوية البريطانية'),
('EK', 'UAE', 'Emirates', 'طيران الإمارات'),
('QR', 'QTR', 'Qatar Airways', 'الخطوط الجوية القطرية'),
('SV', 'SVA', 'Saudi Arabian Airlines', 'الخطوط الجوية السعودية');

INSERT INTO document_types (name_fr, name_ar) VALUES
('Passeport', 'جواز السفر'),
('Carte d\'identité', 'بطاقة الهوية'),
('Visa', 'تأشيرة'),
('Permis de conduire', 'رخصة القيادة'),
('Certificat de naissance', 'شهادة الميلاد'),
('Certificat de mariage', 'شهادة الزواج'),
('Relevé bancaire', 'كشف حساب بنكي'),
('Assurance voyage', 'تأمين السفر'),
('Billet d\'avion', 'تذكرة الطيران'),
('Réservation hôtel', 'حجز فندق');

INSERT INTO clients (code_client, nom, prenom, nom_arabe, prenom_arabe, email, telephone, mobile_1, adresse, ville, code_postal, date_de_naissance, nationality_id, sexe_id, situation_familiale_id, pays_de_naissance_id, lieu_de_naissance_id, compte_comptable_id, label_tel_id, label_mobile_1_id, airline_code_for_loyalty_card_id, loyalty_card_no, related_name, label_related_name_id, tel_related_name) VALUES
('CLI000001', 'HABIBI', 'Karim', 'حبيبي', 'كريم', '<EMAIL>', '0661452223', '0612345678', '16 RUE LALANDE APPT 17 QUARTIER DES HOPITAUX', 'Casablanca', '20360', '1996-04-26', 1, 1, 2, 1, 1, 1, 2, 1, 1, 'RAM19349573', 'habibi samir', 7, '+212701280868'),
('CLI000002', 'BENALI', 'Fatima', 'بن علي', 'فاطمة', '<EMAIL>', '0522334455', '0666778899', '25 AVENUE MOHAMMED V', 'Rabat', '10000', '1985-12-15', 1, 2, 2, 1, 2, 1, 1, 1, 1, 'RAM87654321', 'BENALI Ahmed', 7, '+212661234567'),
('CLI000003', 'MARTIN', 'Jean', NULL, NULL, '<EMAIL>', '0033145678901', '0033612345678', '123 RUE DE LA PAIX', 'Paris', '75001', '1978-08-22', 2, 1, 1, 2, 6, 2, 3, 1, 2, 'AF123456789', 'MARTIN Marie', 7, '+33612345678'),
('CLI000004', 'GARCIA', 'Maria', NULL, NULL, '<EMAIL>', '0034912345678', '0034687654321', 'CALLE MAYOR 45', 'Madrid', '28001', '1990-03-10', 3, 2, 1, 3, 9, 3, 1, 1, 3, 'IB987654321', 'GARCIA Carlos', 8, '+34687654321'),
('CLI000005', 'ALAOUI', 'Mohammed', 'العلوي', 'محمد', '<EMAIL>', '0524556677', '0677889900', 'QUARTIER GUELIZ RUE YOUGOSLAVIE', 'Marrakech', '40000', '1982-11-05', 1, 1, 2, 1, 3, 3, 1, 1, 1, 'RAM55667788', 'ALAOUI Aicha', 7, '+212677889900');

INSERT INTO client_documents (client_id, document_type_id, file_name, file_path, file_size, mime_type) VALUES
(1, 1, 'passeport_habibi_karim.pdf', '/uploads/documents/passeport_habibi_karim.pdf', 2048576, 'application/pdf'),
(1, 2, 'cin_habibi_karim.jpg', '/uploads/documents/cin_habibi_karim.jpg', 1024000, 'image/jpeg'),
(2, 1, 'passeport_benali_fatima.pdf', '/uploads/documents/passeport_benali_fatima.pdf', 1856432, 'application/pdf'),
(3, 1, 'passport_martin_jean.pdf', '/uploads/documents/passport_martin_jean.pdf', 2234567, 'application/pdf'),
(3, 4, 'permis_martin_jean.jpg', '/uploads/documents/permis_martin_jean.jpg', 987654, 'image/jpeg'),
(4, 1, 'pasaporte_garcia_maria.pdf', '/uploads/documents/pasaporte_garcia_maria.pdf', 2100000, 'application/pdf'),
(5, 1, 'passeport_alaoui_mohammed.pdf', '/uploads/documents/passeport_alaoui_mohammed.pdf', 1950000, 'application/pdf'),
(5, 2, 'cin_alaoui_mohammed.jpg', '/uploads/documents/cin_alaoui_mohammed.jpg', 1100000, 'image/jpeg');

INSERT INTO voyages (client_id, destination, date_depart, date_retour, prix, statut, notes) VALUES
(1, 'Paris', '2024-07-15', '2024-07-22', 1250.00, 'Confirmé', 'Vol direct RAM, hôtel 4 étoiles'),
(1, 'Dubai', '2024-12-20', '2024-12-28', 2100.00, 'En attente', 'Voyage de noces'),
(2, 'Istanbul', '2024-06-10', '2024-06-17', 980.00, 'Confirmé', 'Voyage culturel'),
(3, 'Casablanca', '2024-08-05', '2024-08-12', 750.00, 'Confirmé', 'Visite famille'),
(4, 'Londres', '2024-09-01', '2024-09-08', 1450.00, 'Confirmé', 'Voyage d affaires'),
(5, 'Rome', '2024-10-15', '2024-10-22', 1100.00, 'En attente', 'Voyage culturel en famille');