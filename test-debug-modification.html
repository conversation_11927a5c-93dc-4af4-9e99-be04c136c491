<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🐛 Debug Modification</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f0f0f0; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        .result { margin: 10px 0; padding: 15px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { padding: 12px 24px; margin: 8px; border: none; border-radius: 6px; cursor: pointer; font-weight: bold; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        h1 { color: #333; text-align: center; }
        .step { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #f9f9f9; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐛 Debug Modification Client</h1>
        
        <div class="step">
            <h3>Étape 1: Récupérer Client Existant</h3>
            <button class="btn-primary" onclick="getClient()">📋 Récupérer Client ID 1</button>
            <div id="getResult" class="result"></div>
        </div>

        <div class="step">
            <h3>Étape 2: Tester Modification Simple</h3>
            <button class="btn-success" onclick="testSimpleModification()">🔧 Modifier Nom Seulement</button>
            <div id="modifyResult" class="result"></div>
        </div>

        <div class="step">
            <h3>Étape 3: Vérifier Modification</h3>
            <button class="btn-primary" onclick="verifyModification()">🔍 Vérifier Changements</button>
            <div id="verifyResult" class="result"></div>
        </div>

        <div class="step">
            <h3>Étape 4: Test Complet</h3>
            <button class="btn-success" onclick="runCompleteTest()">🚀 Test Complet</button>
            <div id="completeResult" class="result"></div>
        </div>

        <div class="step">
            <h3>🔗 Interface Principale</h3>
            <button class="btn-primary" onclick="window.open('/', '_blank')" style="padding: 15px 30px; font-size: 16px;">
                🖥️ Ouvrir Interface Principale
            </button>
        </div>
    </div>

    <script>
        let originalClient = null;

        // Étape 1: Récupérer le client
        async function getClient() {
            try {
                addResult('getResult', 'info', 'Récupération du client ID 1...');
                
                const response = await fetch('/api/clients/1');
                console.log('GET Response:', response.status, response.statusText);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                originalClient = await response.json();
                console.log('Client récupéré:', originalClient);
                
                addResult('getResult', 'success', `✅ Client récupéré:
ID: ${originalClient.id}
Nom: ${originalClient.nom}
Prénom: ${originalClient.prenom}
Email: ${originalClient.email}
Ville: ${originalClient.ville || 'Non définie'}
Mobile 1: ${originalClient.mobile_1 || 'Non défini'}
Nom Arabe: ${originalClient.nom_arabe || 'Non défini'}`);
                
            } catch (error) {
                console.error('Erreur GET:', error);
                addResult('getResult', 'error', `❌ Erreur: ${error.message}`);
            }
        }

        // Étape 2: Test modification simple
        async function testSimpleModification() {
            if (!originalClient) {
                addResult('modifyResult', 'error', '❌ Veuillez d\'abord récupérer le client');
                return;
            }

            try {
                addResult('modifyResult', 'info', 'Test de modification simple...');
                
                const modificationData = {
                    nom: 'HABIBI_TEST_MODIF',
                    prenom: originalClient.prenom,
                    email: originalClient.email,
                    telephone: originalClient.telephone,
                    ville: 'Casablanca_Test'
                };

                console.log('Données à envoyer:', modificationData);
                
                const response = await fetch('/api/clients/1', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json; charset=utf-8'
                    },
                    body: JSON.stringify(modificationData)
                });

                console.log('PUT Response:', response.status, response.statusText);
                console.log('PUT Headers:', [...response.headers.entries()]);

                const responseText = await response.text();
                console.log('PUT Response Text:', responseText);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${responseText}`);
                }

                let result;
                try {
                    result = JSON.parse(responseText);
                } catch {
                    result = { message: responseText };
                }

                console.log('PUT Result:', result);
                
                addResult('modifyResult', 'success', `✅ Modification envoyée:
Statut: ${response.status}
Réponse: ${JSON.stringify(result, null, 2)}`);
                
            } catch (error) {
                console.error('Erreur PUT:', error);
                addResult('modifyResult', 'error', `❌ Erreur: ${error.message}`);
            }
        }

        // Étape 3: Vérifier la modification
        async function verifyModification() {
            try {
                addResult('verifyResult', 'info', 'Vérification de la modification...');
                
                const response = await fetch('/api/clients/1');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const updatedClient = await response.json();
                console.log('Client mis à jour:', updatedClient);
                
                const comparison = `📊 Comparaison:
Nom: "${originalClient?.nom}" → "${updatedClient.nom}"
Prénom: "${originalClient?.prenom}" → "${updatedClient.prenom}"
Ville: "${originalClient?.ville || 'null'}" → "${updatedClient.ville || 'null'}"
Mobile 1: "${originalClient?.mobile_1 || 'null'}" → "${updatedClient.mobile_1 || 'null'}"

${updatedClient.nom === 'HABIBI_TEST_MODIF' ? '✅ MODIFICATION RÉUSSIE!' : '❌ Modification non détectée'}`;
                
                addResult('verifyResult', updatedClient.nom === 'HABIBI_TEST_MODIF' ? 'success' : 'error', comparison);
                
            } catch (error) {
                console.error('Erreur vérification:', error);
                addResult('verifyResult', 'error', `❌ Erreur: ${error.message}`);
            }
        }

        // Test complet
        async function runCompleteTest() {
            addResult('completeResult', 'info', '🚀 Démarrage du test complet...');
            
            try {
                // Étape 1
                await getClient();
                await sleep(1000);
                
                // Étape 2
                await testSimpleModification();
                await sleep(1000);
                
                // Étape 3
                await verifyModification();
                
                addResult('completeResult', 'success', '🎉 Test complet terminé! Vérifiez les résultats ci-dessus.');
                
            } catch (error) {
                addResult('completeResult', 'error', `❌ Erreur test complet: ${error.message}`);
            }
        }

        // Utilitaires
        function addResult(containerId, type, message) {
            const container = document.getElementById(containerId);
            container.className = `result ${type}`;
            container.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
        }

        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // Auto-start
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🐛 Page de debug chargée');
        });
    </script>
</body>
</html>
