<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Test Export Excel Simple</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f0f0f0; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        button { padding: 15px 25px; margin: 10px; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; font-size: 16px; }
        .btn-excel { background: #217346; color: white; }
        .btn-test { background: #007bff; color: white; }
        .result { margin: 15px 0; padding: 20px; border-radius: 8px; font-family: monospace; white-space: pre-wrap; }
        .success { background: #d4edda; color: #155724; border: 2px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 2px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 2px solid #bee5eb; }
        h1 { color: #333; text-align: center; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 TEST EXPORT EXCEL - CORRECTION APPLIQUÉE</h1>
        
        <div class="info result">
✅ CORRECTIONS APPLIQUÉES:

1. Route d'export déplacée AVANT les routes avec paramètres
2. Suppression de la route en double
3. Fonction JavaScript améliorée avec logs détaillés
4. Meilleure gestion d'erreur
5. Vérification de la taille du blob
6. Headers HTTP appropriés

🧪 L'API backend fonctionne parfaitement (testé avec PowerShell) !
📊 Fichier Excel généré: 11KB avec 3 clients
        </div>

        <h3>🧪 Tests Directs</h3>
        
        <button class="btn-test" onclick="testAPI()">
            🔍 Tester API Backend
        </button>
        
        <button class="btn-excel" onclick="testExportDirect()">
            📊 Test Export Excel Direct
        </button>
        
        <button class="btn-excel" onclick="testExportWithLogs()">
            📋 Test Export avec Logs Détaillés
        </button>

        <div id="result" class="result info">Cliquez sur un bouton pour tester...</div>

        <h3>🚀 Interface Principale</h3>
        <button class="btn-test" onclick="window.open('/', '_blank')">
            🖥️ Ouvrir Interface Principale
        </button>
    </div>

    <script>
        // Test API backend
        async function testAPI() {
            const result = document.getElementById('result');
            result.className = 'result info';
            result.textContent = 'Test API en cours...';
            
            try {
                const response = await fetch('/api/clients/export/excel', { method: 'HEAD' });
                
                result.className = 'result success';
                result.textContent = `✅ API Backend fonctionne parfaitement !
                
Status: ${response.status} ${response.statusText}
Headers: ${JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2)}

L'API génère bien le fichier Excel !`;
                
            } catch (error) {
                result.className = 'result error';
                result.textContent = `❌ Erreur API: ${error.message}`;
            }
        }

        // Test export direct
        async function testExportDirect() {
            const result = document.getElementById('result');
            result.className = 'result info';
            result.textContent = 'Test export direct en cours...';
            
            try {
                const response = await fetch('/api/clients/export/excel');
                
                if (!response.ok) {
                    throw new Error(`Erreur ${response.status}: ${response.statusText}`);
                }
                
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                
                link.href = url;
                link.download = `test_direct_export_${new Date().toISOString().split('T')[0]}.xlsx`;
                link.click();
                
                window.URL.revokeObjectURL(url);
                
                result.className = 'result success';
                result.textContent = `✅ Export direct réussi !
                
Fichier téléchargé: test_direct_export_${new Date().toISOString().split('T')[0]}.xlsx
Taille: ${(blob.size / 1024).toFixed(2)} KB
Type: ${blob.type}

Le fichier a été téléchargé dans votre dossier de téléchargements !`;
                
            } catch (error) {
                result.className = 'result error';
                result.textContent = `❌ Erreur export direct: ${error.message}`;
            }
        }

        // Test export avec logs détaillés (comme dans l'interface principale)
        async function testExportWithLogs() {
            const result = document.getElementById('result');
            result.className = 'result info';
            result.textContent = 'Test export avec logs détaillés...';
            
            try {
                console.log('📤 Début de l\'export Excel...');
                
                console.log('🔗 Appel API: /api/clients/export/excel');
                const response = await fetch('/api/clients/export/excel', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    }
                });

                console.log('📡 Réponse API:', {
                    status: response.status,
                    statusText: response.statusText,
                    headers: Object.fromEntries(response.headers.entries())
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('❌ Erreur API:', errorText);
                    throw new Error(`Erreur ${response.status}: ${errorText}`);
                }

                console.log('📦 Conversion en blob...');
                const blob = await response.blob();
                console.log('📊 Blob créé:', {
                    size: blob.size,
                    type: blob.type
                });

                if (blob.size === 0) {
                    throw new Error('Le fichier Excel généré est vide');
                }

                console.log('🔗 Création du lien de téléchargement...');
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                
                const fileName = `test_logs_export_${new Date().toISOString().split('T')[0]}.xlsx`;
                link.href = url;
                link.download = fileName;
                link.style.display = 'none';
                
                document.body.appendChild(link);
                console.log('⬇️ Déclenchement du téléchargement...');
                link.click();
                document.body.removeChild(link);
                
                window.URL.revokeObjectURL(url);

                console.log('✅ Export Excel terminé avec succès');
                
                result.className = 'result success';
                result.textContent = `✅ Export avec logs détaillés réussi !
                
Fichier téléchargé: ${fileName}
Taille: ${(blob.size / 1024).toFixed(2)} KB
Type: ${blob.type}

Vérifiez la console (F12) pour voir tous les logs détaillés !
Le fichier a été téléchargé dans votre dossier de téléchargements !`;
                
            } catch (error) {
                console.error('❌ Erreur export Excel complète:', error);
                console.error('❌ Stack trace:', error.stack);
                
                result.className = 'result error';
                result.textContent = `❌ Erreur export avec logs: ${error.message}
                
Vérifiez la console (F12) pour plus de détails.`;
            }
        }

        // Auto-start
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🧪 Page de test export Excel simple chargée');
        });
    </script>
</body>
</html>
