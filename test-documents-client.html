<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📄 Test Documents Client</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f0f0f0; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        .result { margin: 15px 0; padding: 20px; border-radius: 8px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto; }
        .success { background: #d4edda; color: #155724; border: 2px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 2px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 2px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 2px solid #ffeaa7; }
        button { padding: 15px 25px; margin: 10px; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; font-size: 16px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        h1 { color: #333; text-align: center; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #f9f9f9; }
        .client-list { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; max-height: 200px; overflow-y: auto; }
        .client-item { padding: 10px; margin: 5px 0; background: white; border-radius: 5px; border: 1px solid #ddd; cursor: pointer; }
        .client-item:hover { background: #e9ecef; }
        .client-item.selected { background: #007bff; color: white; }
        .upload-test { border: 2px dashed #ddd; padding: 30px; text-align: center; border-radius: 8px; margin: 15px 0; }
        .upload-test.dragover { border-color: #007bff; background: rgba(0, 123, 255, 0.1); }
        input[type="file"] { margin: 10px 0; }
        .document-list { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .document-item { padding: 10px; margin: 5px 0; background: white; border-radius: 5px; border: 1px solid #ddd; display: flex; justify-content: space-between; align-items: center; }
    </style>
</head>
<body>
    <div class="container">
        <h1>📄 Test Documents Client</h1>
        
        <div class="test-section">
            <h3>📊 Corrections Appliquées</h3>
            <div class="success result">
✅ Fonction initializeDocumentDragDrop() ajoutée
✅ Event listeners drag & drop configurés
✅ Upload multiple de fichiers supporté
✅ Styles CSS dragover ajoutés
✅ Logs de debugging renforcés
✅ Gestion d'erreurs améliorée
            </div>
        </div>

        <div class="test-section">
            <h3>1. Sélection du Client</h3>
            <button class="btn-primary" onclick="loadClients()">📋 Charger Clients</button>
            <div id="clientsList" class="client-list"></div>
            <div id="selectedClient" class="info result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>2. Test Upload Documents</h3>
            <div id="uploadSection" style="display: none;">
                <div class="upload-test" id="uploadZone">
                    <i class="fas fa-cloud-upload-alt" style="font-size: 3rem; color: #6c757d; margin-bottom: 15px;"></i>
                    <h5>Glisser-déposer vos fichiers ici</h5>
                    <p>ou cliquez pour sélectionner</p>
                    <input type="file" id="documentInput" accept=".pdf,.jpg,.jpeg,.png,.gif" multiple style="margin: 15px 0;">
                </div>
                
                <div style="margin: 15px 0;">
                    <label for="documentType">Type de document:</label>
                    <select id="documentType" style="padding: 8px; margin: 5px; border-radius: 4px;">
                        <option value="passeport">🛂 Passeport</option>
                        <option value="carte_identite">🆔 Carte d'identité</option>
                        <option value="permis_conduire">🚗 Permis de conduire</option>
                        <option value="autre">📄 Autre document</option>
                    </select>
                </div>
                
                <button class="btn-success" onclick="uploadDocument()">📤 Upload Document</button>
            </div>
            <div id="uploadResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. Documents du Client</h3>
            <button class="btn-primary" onclick="loadDocuments()" id="loadDocsBtn" style="display: none;">📄 Charger Documents</button>
            <div id="documentsList" class="document-list"></div>
            <div id="documentsResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. Test Interface Principale</h3>
            <button class="btn-primary" onclick="window.open('/', '_blank')" style="padding: 20px 30px; font-size: 18px;">
                🖥️ Ouvrir Interface Principale
            </button>
            <div class="warning result">
📋 INSTRUCTIONS POUR TESTER DANS L'INTERFACE PRINCIPALE:

1. Ouvrez l'interface principale
2. Cliquez "Modifier" sur un client existant
3. Allez dans l'onglet "Documents"
4. Testez le drag & drop de fichiers
5. Testez l'upload via le bouton
6. Vérifiez que les documents s'affichent
7. Testez le téléchargement et la suppression

RÉSULTAT ATTENDU:
✅ Zone de drag & drop fonctionnelle
✅ Upload de fichiers multiples
✅ Affichage des documents
✅ Téléchargement et suppression
            </div>
        </div>
    </div>

    <script>
        let selectedClientId = null;
        let clientDocuments = [];

        // Charger les clients
        async function loadClients() {
            try {
                addResult('clientsList', 'info', 'Chargement des clients...');
                
                const response = await fetch('/api/clients');
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                
                const clients = await response.json();
                
                const clientsList = document.getElementById('clientsList');
                clientsList.innerHTML = clients.map(client => `
                    <div class="client-item" onclick="selectClient(${client.id}, '${client.nom}', '${client.prenom}')">
                        <strong>${client.nom} ${client.prenom}</strong><br>
                        <small>${client.email} - Code: ${client.code_client || 'N/A'}</small>
                    </div>
                `).join('');
                
                addResult('clientsList', 'success', `${clients.length} clients chargés`);
                
            } catch (error) {
                addResult('clientsList', 'error', `Erreur: ${error.message}`);
            }
        }

        // Sélectionner un client
        function selectClient(clientId, nom, prenom) {
            selectedClientId = clientId;
            
            // Mettre à jour l'affichage
            document.querySelectorAll('.client-item').forEach(item => {
                item.classList.remove('selected');
            });
            event.target.classList.add('selected');
            
            const selectedDiv = document.getElementById('selectedClient');
            selectedDiv.style.display = 'block';
            selectedDiv.textContent = `Client sélectionné: ${nom} ${prenom} (ID: ${clientId})`;
            selectedDiv.className = 'success result';
            
            // Afficher la section upload
            document.getElementById('uploadSection').style.display = 'block';
            document.getElementById('loadDocsBtn').style.display = 'inline-block';
            
            // Initialiser le drag & drop
            initializeDragDrop();
            
            // Charger les documents existants
            loadDocuments();
        }

        // Initialiser le drag & drop
        function initializeDragDrop() {
            const uploadZone = document.getElementById('uploadZone');
            const documentInput = document.getElementById('documentInput');
            
            uploadZone.onclick = () => documentInput.click();
            
            uploadZone.ondragover = (e) => {
                e.preventDefault();
                uploadZone.classList.add('dragover');
            };
            
            uploadZone.ondragleave = (e) => {
                e.preventDefault();
                uploadZone.classList.remove('dragover');
            };
            
            uploadZone.ondrop = (e) => {
                e.preventDefault();
                uploadZone.classList.remove('dragover');
                documentInput.files = e.dataTransfer.files;
                if (documentInput.files.length > 0) {
                    uploadDocument();
                }
            };
        }

        // Upload document
        async function uploadDocument() {
            if (!selectedClientId) {
                addResult('uploadResult', 'error', 'Aucun client sélectionné');
                return;
            }

            const files = document.getElementById('documentInput').files;
            if (!files || files.length === 0) {
                addResult('uploadResult', 'error', 'Aucun fichier sélectionné');
                return;
            }

            try {
                addResult('uploadResult', 'info', `Upload de ${files.length} fichier(s)...`);
                
                for (let i = 0; i < files.length; i++) {
                    const file = files[i];
                    const formData = new FormData();
                    formData.append('document', file);
                    formData.append('type_document', document.getElementById('documentType').value);

                    const response = await fetch(`/api/upload/document/${selectedClientId}`, {
                        method: 'POST',
                        body: formData
                    });

                    if (!response.ok) {
                        const error = await response.json();
                        throw new Error(error.error || 'Erreur upload');
                    }

                    const result = await response.json();
                    console.log('Document uploadé:', result);
                }

                addResult('uploadResult', 'success', `✅ ${files.length} document(s) uploadé(s) avec succès!`);
                document.getElementById('documentInput').value = '';
                loadDocuments();
                
            } catch (error) {
                addResult('uploadResult', 'error', `❌ Erreur: ${error.message}`);
            }
        }

        // Charger les documents
        async function loadDocuments() {
            if (!selectedClientId) return;

            try {
                addResult('documentsResult', 'info', 'Chargement des documents...');
                
                const response = await fetch(`/api/upload/documents/${selectedClientId}`);
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                
                clientDocuments = await response.json();
                
                const documentsList = document.getElementById('documentsList');
                if (clientDocuments.length === 0) {
                    documentsList.innerHTML = '<div style="text-align: center; color: #6c757d; padding: 20px;">Aucun document</div>';
                } else {
                    documentsList.innerHTML = clientDocuments.map(doc => `
                        <div class="document-item">
                            <div>
                                <strong>${getDocumentTypeLabel(doc.type_document)}</strong><br>
                                <small>${doc.nom_fichier} (${formatFileSize(doc.taille_fichier)})</small>
                            </div>
                            <div>
                                <button class="btn-primary" onclick="downloadDocument(${doc.id})" style="padding: 5px 10px; margin: 2px;">⬇️</button>
                                <button class="btn-danger" onclick="deleteDocument(${doc.id})" style="padding: 5px 10px; margin: 2px;">🗑️</button>
                            </div>
                        </div>
                    `).join('');
                }
                
                addResult('documentsResult', 'success', `${clientDocuments.length} document(s) trouvé(s)`);
                
            } catch (error) {
                addResult('documentsResult', 'error', `Erreur: ${error.message}`);
            }
        }

        // Télécharger document
        function downloadDocument(documentId) {
            window.open(`/api/upload/document/${documentId}/download`, '_blank');
        }

        // Supprimer document
        async function deleteDocument(documentId) {
            if (!confirm('Supprimer ce document ?')) return;

            try {
                const response = await fetch(`/api/upload/document/${documentId}`, {
                    method: 'DELETE'
                });

                if (!response.ok) throw new Error(`HTTP ${response.status}`);

                addResult('documentsResult', 'success', 'Document supprimé');
                loadDocuments();
                
            } catch (error) {
                addResult('documentsResult', 'error', `Erreur: ${error.message}`);
            }
        }

        // Utilitaires
        function getDocumentTypeLabel(type) {
            const types = {
                'passeport': '🛂 Passeport',
                'carte_identite': '🆔 Carte d\'identité',
                'permis_conduire': '🚗 Permis de conduire',
                'autre': '📄 Autre'
            };
            return types[type] || '📄 Document';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function addResult(containerId, type, message) {
            const container = document.getElementById(containerId);
            if (container.classList.contains('client-list') || container.classList.contains('document-list')) {
                // Pour les listes, on ne change pas le contenu
                return;
            }
            container.className = `result ${type}`;
            container.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
        }

        // Auto-start
        document.addEventListener('DOMContentLoaded', () => {
            console.log('📄 Page de test documents chargée');
            loadClients();
        });
    </script>
</body>
</html>
