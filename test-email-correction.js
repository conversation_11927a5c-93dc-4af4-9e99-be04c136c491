// Test direct de la correction des emails en double
const express = require('express');
const app = express();
app.use(express.json());

// Simuler la fonction de correction
function testEmailCorrection() {
    console.log('🧪 Test de la correction des emails en double...\n');

    // Simuler des clients avec emails en double
    const testClients = [
        {
            nom: 'DUPONT',
            prenom: '<PERSON>',
            email: '<EMAIL>',
            _rowNumber: 2
        },
        {
            nom: 'MARTIN',
            prenom: 'Marie',
            email: '<EMAIL>', // Email en double
            _rowNumber: 3
        }
    ];

    testClients.forEach(client => {
        const { _rowNumber, ...clientData } = client;
        
        // Simuler l'erreur SQLite
        const err = {
            code: 'SQLITE_CONSTRAINT',
            message: 'SQLITE_CONSTRAINT: UNIQUE constraint failed: clients.email'
        };

        console.log(`🔄 Test client ligne ${_rowNumber}: ${clientData.nom} ${clientData.prenom} - ${clientData.email}`);

        // Test de la condition de détection
        if (err && (err.code === 'SQLITE_CONSTRAINT' || err.code === 'SQLITE_CONSTRAINT_UNIQUE') && 
            (err.message.includes('email') || err.message.includes('UNIQUE constraint failed: clients.email'))) {
            
            console.log(`✅ Erreur email détectée correctement pour ligne ${_rowNumber}`);
            
            // Générer email unique
            const originalEmail = clientData.email;
            const timestamp = Date.now();
            const emailParts = originalEmail.split('@');
            const uniqueEmail = `${emailParts[0]}_${timestamp}@${emailParts[1]}`;
            
            console.log(`🔄 Email original: ${originalEmail}`);
            console.log(`🔄 Email unique généré: ${uniqueEmail}`);
            console.log(`✅ Correction appliquée avec succès !\n`);
        } else {
            console.log(`❌ Condition de détection non remplie pour ligne ${_rowNumber}\n`);
        }
    });

    console.log('🎯 Test de correction terminé !');
}

// Exécuter le test
testEmailCorrection();

// Test avec le vrai serveur
async function testWithRealServer() {
    console.log('\n🌐 Test avec le serveur réel...');
    
    const testData = {
        clients: [
            {
                nom: 'TEST_CORRECTION',
                prenom: 'Email1',
                email: `test.correction.${Date.now()}@example.com`,
                _rowNumber: 2
            }
        ]
    };

    try {
        const response = await fetch('http://localhost:3000/api/clients/import/confirm', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(testData)
        });

        const result = await response.json();
        console.log('📊 Résultat du test serveur:', result);
        
    } catch (error) {
        console.log('❌ Erreur test serveur:', error.message);
    }
}

// Attendre un peu puis tester avec le serveur
setTimeout(testWithRealServer, 2000);
