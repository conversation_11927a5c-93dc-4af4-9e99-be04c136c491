<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 Test Automatique Import Excel</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: linear-gradient(135deg, #007bff 0%, #6610f2 100%); min-height: 100vh; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        .result { margin: 15px 0; padding: 20px; border-radius: 8px; font-family: monospace; white-space: pre-wrap; }
        .success { background: #d4edda; color: #155724; border: 2px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 2px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 2px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 2px solid #ffeaa7; }
        button { padding: 15px 25px; margin: 10px; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; font-size: 16px; transition: all 0.3s; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        button:hover { transform: translateY(-2px); box-shadow: 0 4px 15px rgba(0,0,0,0.2); }
        h1 { color: #333; text-align: center; margin-bottom: 30px; }
        .auto-highlight { background: linear-gradient(45deg, #007bff, #6610f2); color: white; padding: 20px; border-radius: 10px; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 TEST AUTOMATIQUE - CORRECTION EMAILS</h1>
        
        <div class="auto-highlight">
            <h2>🔧 TEST AUTOMATIQUE DE LA CORRECTION</h2>
            <p><strong>Cette page teste automatiquement la correction des emails en double</strong> en simulant un import avec des conflits d'emails.</p>
        </div>

        <div class="info result">
🎯 OBJECTIF DU TEST:

1. Créer des clients avec emails en conflit
2. Tenter l'import via l'API
3. Vérifier que la correction automatique fonctionne
4. Confirmer que tous les clients sont importés

📧 EMAILS DE TEST:
- <EMAIL> (utilisé 2 fois)
- <EMAIL> (unique)
        </div>

        <button class="btn-primary" onclick="runAutoTest()">
            🤖 Lancer Test Automatique
        </button>
        
        <button class="btn-success" onclick="testWithExistingEmail()">
            📧 Tester avec Email Existant
        </button>
        
        <button class="btn-danger" onclick="clearResults()">
            🗑️ Effacer Résultats
        </button>

        <div id="result" class="result info">Cliquez sur "Lancer Test Automatique" pour commencer...</div>

        <div class="warning result">
💡 CE TEST VA:

1. Créer 3 clients avec des emails en conflit
2. Les envoyer à l'API d'import
3. Vérifier que la correction automatique fonctionne
4. Afficher les résultats détaillés

🔍 Surveillez les logs du serveur pour voir la correction en action !
        </div>
    </div>

    <script>
        // Test automatique complet
        async function runAutoTest() {
            const result = document.getElementById('result');
            result.className = 'result info';
            result.textContent = '🤖 Démarrage du test automatique...\n';

            try {
                // Créer des clients avec emails en conflit
                const timestamp = Date.now();
                const testClients = [
                    {
                        nom: 'AUTO_TEST',
                        prenom: 'Client1',
                        email: '<EMAIL>',
                        telephone: '0123456789',
                        _rowNumber: 2
                    },
                    {
                        nom: 'AUTO_TEST',
                        prenom: 'Client2',
                        email: `test.auto.unique.${timestamp}@example.com`,
                        telephone: '0234567890',
                        _rowNumber: 3
                    },
                    {
                        nom: 'AUTO_TEST',
                        prenom: 'Client3',
                        email: '<EMAIL>', // Même email que Client1
                        telephone: '0345678901',
                        _rowNumber: 4
                    }
                ];

                result.textContent += '📋 Clients de test créés:\n';
                testClients.forEach((client, index) => {
                    result.textContent += `   ${index + 1}. ${client.nom} ${client.prenom} - ${client.email}\n`;
                });
                result.textContent += '\n🚀 Envoi à l\'API d\'import...\n';

                // Envoyer à l'API d'import
                const response = await fetch('/api/clients/import/confirm', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ clients: testClients })
                });

                if (!response.ok) {
                    throw new Error(`Erreur API: ${response.status}`);
                }

                const data = await response.json();
                
                result.className = 'result success';
                result.textContent += `✅ TEST AUTOMATIQUE TERMINÉ !\n\n`;
                result.textContent += `📊 RÉSULTATS:\n`;
                result.textContent += `   - Clients traités: ${data.total}\n`;
                result.textContent += `   - Imports réussis: ${data.imported}\n`;
                result.textContent += `   - Erreurs: ${data.errors.length}\n\n`;

                if (data.errors.length > 0) {
                    result.textContent += `⚠️ DÉTAILS DES ERREURS:\n`;
                    data.errors.forEach(error => {
                        result.textContent += `   - ${error}\n`;
                    });
                } else {
                    result.textContent += `🎉 AUCUNE ERREUR ! La correction automatique a fonctionné !\n`;
                }

                result.textContent += `\n🔍 Vérifiez les logs du serveur pour voir les détails de la correction automatique.`;

                // Analyser les résultats
                if (data.imported === testClients.length) {
                    result.textContent += `\n\n✅ SUCCÈS COMPLET ! Tous les clients ont été importés.`;
                    result.textContent += `\n🔧 La correction automatique des emails fonctionne parfaitement !`;
                } else {
                    result.className = 'result warning';
                    result.textContent += `\n\n⚠️ SUCCÈS PARTIEL: ${data.imported}/${testClients.length} clients importés.`;
                }

            } catch (error) {
                result.className = 'result error';
                result.textContent += `\n❌ ERREUR TEST: ${error.message}`;
            }
        }

        // Test avec un email existant connu
        async function testWithExistingEmail() {
            const result = document.getElementById('result');
            result.className = 'result info';
            result.textContent = '📧 Test avec email existant...\n';

            try {
                // Utiliser un email qui existe probablement déjà
                const testClient = {
                    nom: 'TEST_EXISTING',
                    prenom: 'Email',
                    email: '<EMAIL>', // Email qui existe déjà
                    telephone: '0999999999',
                    _rowNumber: 2
                };

                result.textContent += `📋 Client de test: ${testClient.nom} ${testClient.prenom} - ${testClient.email}\n`;
                result.textContent += '🚀 Envoi à l\'API...\n';

                const response = await fetch('/api/clients/import/confirm', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ clients: [testClient] })
                });

                const data = await response.json();
                
                if (data.imported > 0) {
                    result.className = 'result success';
                    result.textContent += `✅ SUCCÈS ! Email en conflit corrigé automatiquement !\n`;
                } else {
                    result.className = 'result warning';
                    result.textContent += `⚠️ Import échoué. Vérifiez les logs pour plus de détails.\n`;
                }

                result.textContent += `\n📊 RÉSULTATS:\n`;
                result.textContent += `   - Imports réussis: ${data.imported}\n`;
                result.textContent += `   - Erreurs: ${data.errors.length}\n`;
                
                if (data.errors.length > 0) {
                    result.textContent += `\n⚠️ ERREURS:\n`;
                    data.errors.forEach(error => {
                        result.textContent += `   - ${error}\n`;
                    });
                }

            } catch (error) {
                result.className = 'result error';
                result.textContent += `\n❌ ERREUR: ${error.message}`;
            }
        }

        // Effacer les résultats
        function clearResults() {
            const result = document.getElementById('result');
            result.className = 'result info';
            result.textContent = 'Résultats effacés. Prêt pour un nouveau test...';
        }

        // Auto-start
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🤖 Page de test automatique chargée');
        });
    </script>
</body>
</html>
