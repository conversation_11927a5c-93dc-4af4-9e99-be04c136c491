<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Test Frontend Simple</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f0f0f0; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        .form-group { margin: 15px 0; }
        label { display: block; font-weight: bold; margin-bottom: 5px; }
        input { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 16px; }
        button { padding: 15px 25px; margin: 10px 5px; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; font-size: 16px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .result { margin: 15px 0; padding: 20px; border-radius: 8px; font-family: monospace; white-space: pre-wrap; }
        .success { background: #d4edda; color: #155724; border: 2px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 2px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 2px solid #bee5eb; }
        h1 { color: #333; text-align: center; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Frontend Simple</h1>
        
        <form id="testForm">
            <div class="form-group">
                <label for="nom">Nom *</label>
                <input type="text" id="nom" name="nom" required placeholder="Nom de famille">
            </div>
            <div class="form-group">
                <label for="prenom">Prénom *</label>
                <input type="text" id="prenom" name="prenom" required placeholder="Prénom">
            </div>
            <div class="form-group">
                <label for="email">Email *</label>
                <input type="email" id="email" name="email" required placeholder="Email">
            </div>
            <div class="form-group">
                <label for="telephone">Téléphone</label>
                <input type="tel" id="telephone" name="telephone" placeholder="Téléphone">
            </div>
            
            <button type="submit" class="btn-success">💾 Ajouter Client</button>
            <button type="button" class="btn-primary" onclick="testAPI()">🧪 Test API Direct</button>
        </form>
        
        <div id="result" class="result"></div>
        
        <div style="margin-top: 30px; text-align: center;">
            <button class="btn-primary" onclick="window.open('/', '_blank')" style="padding: 15px 30px;">
                🖥️ Interface Principale
            </button>
        </div>
    </div>

    <script>
        // Test de soumission du formulaire
        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            console.log('📝 Soumission du formulaire de test...');
            
            try {
                addResult('info', 'Soumission du formulaire...');
                
                // Récupérer les données
                const clientData = {
                    nom: document.getElementById('nom').value.trim(),
                    prenom: document.getElementById('prenom').value.trim(),
                    email: document.getElementById('email').value.trim(),
                    telephone: document.getElementById('telephone').value.trim() || null
                };

                console.log('📋 Données à envoyer:', clientData);
                addResult('info', `Données préparées:\n${JSON.stringify(clientData, null, 2)}`);

                // Validation
                if (!clientData.nom || !clientData.prenom || !clientData.email) {
                    throw new Error('Les champs nom, prénom et email sont obligatoires');
                }

                // Envoi à l'API
                const response = await fetch('/api/clients', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json; charset=utf-8'
                    },
                    body: JSON.stringify(clientData)
                });

                console.log('📥 Réponse:', response.status, response.statusText);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('❌ Erreur réponse:', errorText);
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }

                const result = await response.json();
                console.log('✅ Résultat:', result);

                addResult('success', `✅ CLIENT AJOUTÉ AVEC SUCCÈS!
ID: ${result.id}
Message: ${result.message}

Le formulaire frontend fonctionne parfaitement!`);

                // Réinitialiser le formulaire
                document.getElementById('testForm').reset();

            } catch (error) {
                console.error('❌ Erreur:', error);
                addResult('error', `❌ ERREUR: ${error.message}

Vérifiez la console (F12) pour plus de détails.`);
            }
        });

        // Test API direct
        async function testAPI() {
            try {
                addResult('info', 'Test API direct...');
                
                const response = await fetch('/api/clients');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const clients = await response.json();
                
                addResult('success', `✅ API FONCTIONNELLE!
Nombre de clients: ${clients.length}

L'API backend fonctionne parfaitement!`);
                
            } catch (error) {
                addResult('error', `❌ Erreur API: ${error.message}`);
            }
        }

        // Utilitaire pour afficher les résultats
        function addResult(type, message) {
            const container = document.getElementById('result');
            container.className = `result ${type}`;
            container.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
        }

        // Test automatique au chargement
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🧪 Page de test frontend chargée');
            
            // Pré-remplir le formulaire pour test rapide
            document.getElementById('nom').value = 'TEST_FRONTEND';
            document.getElementById('prenom').value = 'Client';
            document.getElementById('email').value = `test.frontend.${Date.now()}@example.com`;
            document.getElementById('telephone').value = '0123456789';
            
            addResult('info', 'Formulaire pré-rempli pour test rapide.\nCliquez "Ajouter Client" pour tester!');
        });
    </script>
</body>
</html>
