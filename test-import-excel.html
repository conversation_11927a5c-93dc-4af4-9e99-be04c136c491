<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📥 Test Import Excel - Clients</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        .result { margin: 15px 0; padding: 20px; border-radius: 8px; font-family: monospace; white-space: pre-wrap; }
        .success { background: #d4edda; color: #155724; border: 2px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 2px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 2px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 2px solid #ffeaa7; }
        button { padding: 15px 25px; margin: 10px; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; font-size: 16px; transition: all 0.3s; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-excel { background: #217346; color: white; }
        button:hover { transform: translateY(-2px); box-shadow: 0 4px 15px rgba(0,0,0,0.2); }
        h1 { color: #333; text-align: center; margin-bottom: 30px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #f9f9f9; }
        .test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
        .test-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .feature-highlight { background: linear-gradient(45deg, #28a745, #20c997); color: white; padding: 20px; border-radius: 10px; margin: 15px 0; }
        .upload-zone { border: 2px dashed #007bff; border-radius: 10px; padding: 40px; text-align: center; margin: 20px 0; cursor: pointer; transition: all 0.3s; }
        .upload-zone:hover { border-color: #0056b3; background: #f8f9fa; }
        .upload-zone.dragover { border-color: #28a745; background: #e8f5e8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>📥 TEST IMPORT EXCEL - NOUVELLE FONCTIONNALITÉ</h1>
        
        <div class="feature-highlight">
            <h2>🎉 NOUVELLE FONCTIONNALITÉ : IMPORT EXCEL</h2>
            <p><strong>✅ Implémentée avec succès !</strong> Vous pouvez maintenant importer vos clients depuis des fichiers Excel (.xlsx) avec validation complète et aperçu avant import.</p>
        </div>

        <div class="test-section">
            <h3>📋 Fonctionnalités d'Import Disponibles</h3>
            <div class="test-grid">
                <div class="test-card">
                    <h4>📊 Import Excel (.xlsx)</h4>
                    <div class="success result">
✅ NOUVEAU : Support fichiers Excel
✅ Lecture de toutes les 33 colonnes
✅ Validation des données
✅ Aperçu avant import
✅ Gestion des erreurs détaillée
✅ Import par lots optimisé
                    </div>
                    <div class="upload-zone" onclick="selectExcelFile()" id="excelUploadZone">
                        <i class="fas fa-file-excel" style="font-size: 3em; color: #217346; margin-bottom: 15px;"></i>
                        <h4>Cliquez ou glissez votre fichier Excel ici</h4>
                        <p>Formats supportés : .xlsx, .xls</p>
                        <p style="font-size: 0.9em; color: #666;">Taille max : 50MB</p>
                    </div>
                    <div id="excelResult" class="result"></div>
                </div>
                
                <div class="test-card">
                    <h4>📄 Import CSV/JSON (Existant)</h4>
                    <div class="info result">
✅ Format CSV classique
✅ Format JSON pour développeurs
✅ Compatible avec exports existants
✅ Validation basique
                    </div>
                    <button class="btn-warning" onclick="testLegacyImport()">
                        <i class="fas fa-file-csv"></i> Tester Import CSV/JSON
                    </button>
                    <div id="legacyResult" class="result"></div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 Détails Techniques de l'Import Excel</h3>
            <div class="info result">
📋 COLONNES SUPPORTÉES (33 au total):

Même structure que l'export Excel :
1. Code Client          18. Pays de Naissance ID
2. Nom                  19. Lieu de Naissance ID  
3. Prénom               20. Compte Comptable ID
4. Email                21. Label Tél ID
5. Téléphone            22. Label Mobile 1 ID
6. Mobile 1             23. Label Mobile 2 ID
7. Mobile 2             24. Nom Arabe
8. Adresse              25. Prénom Arabe
9. Ville                26. N° Carte Fidélité
10. Code Postal         27. Compagnie Aérienne ID
11. CIN N°              28. Nom Personne Liée
12. Passeport N°        29. Label Personne Liée ID
13. Date Expiration     30. Tél Personne Liée
14. Date de Naissance   31. Photo URL
15. Nationalité ID      32. Date Création
16. Sexe ID             33. Date Modification
17. Situation Familiale ID

🔧 FONCTIONNALITÉS:
- Lecture automatique de la première feuille Excel
- Conversion des dates Excel en format ISO
- Validation des champs obligatoires (nom, prénom)
- Aperçu des données avant import
- Rapport d'erreurs détaillé
- Import par lots pour éviter les conflits
            </div>
        </div>

        <div class="test-section">
            <h3>📊 Créer un Fichier Excel de Test</h3>
            <div class="test-grid">
                <div class="test-card">
                    <h4>🎯 Méthode Recommandée</h4>
                    <div class="warning result">
📋 PROCÉDURE POUR CRÉER UN FICHIER DE TEST:

1. Exportez d'abord vos clients existants en Excel
2. Ouvrez le fichier dans Excel/LibreOffice
3. Modifiez les données ou ajoutez de nouvelles lignes
4. Sauvegardez le fichier
5. Importez-le avec cette nouvelle fonctionnalité

💡 Cette méthode garantit la compatibilité parfaite !
                    </div>
                    <button class="btn-excel" onclick="window.open('/', '_blank')">
                        📊 Aller à l'Export Excel
                    </button>
                </div>
                
                <div class="test-card">
                    <h4>📝 Fichier Excel Manuel</h4>
                    <div class="info result">
📋 STRUCTURE MINIMALE REQUISE:

Colonnes obligatoires :
- Colonne A: Code Client (peut être vide, sera généré)
- Colonne B: Nom (obligatoire)
- Colonne C: Prénom (obligatoire)
- Colonne D: Email (recommandé)

Les autres colonnes sont optionnelles.
Première ligne = headers (sera ignorée).
                    </div>
                    <button class="btn-primary" onclick="downloadSampleExcel()">
                        📥 Télécharger Modèle Excel
                    </button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🚀 Interface Principale</h3>
            <button class="btn-primary" onclick="window.open('/', '_blank')">
                🖥️ Ouvrir Interface Principale
            </button>
            <div class="warning result">
💡 Dans l'interface principale, cliquez sur "Importer" puis choisissez "Excel (.xlsx)" pour utiliser la nouvelle fonctionnalité !
            </div>
        </div>
    </div>

    <script>
        // Sélectionner un fichier Excel
        function selectExcelFile() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.xlsx,.xls';
            input.onchange = handleExcelTest;
            input.click();
        }

        // Test d'import Excel
        async function handleExcelTest(event) {
            const file = event.target.files[0];
            if (!file) return;

            const result = document.getElementById('excelResult');
            result.className = 'result info';
            result.textContent = `Test import Excel en cours...
            
Fichier: ${file.name}
Taille: ${(file.size / 1024).toFixed(2)} KB
Type: ${file.type}`;

            try {
                const formData = new FormData();
                formData.append('excelFile', file);

                const response = await fetch('/api/clients/import/excel', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error);
                }

                const data = await response.json();
                
                result.className = 'result success';
                result.textContent = `✅ Test import Excel réussi !
                
📊 RÉSULTATS:
- Lignes totales: ${data.preview.totalRows}
- Clients valides: ${data.preview.validClients}
- Erreurs: ${data.preview.errors.length}

📋 APERÇU DES DONNÉES:
${data.preview.clients.slice(0, 3).map(c => `- ${c.nom} ${c.prenom} (${c.email})`).join('\n')}

${data.preview.errors.length > 0 ? `\n⚠️ ERREURS:\n${data.preview.errors.slice(0, 3).join('\n')}` : ''}

🎯 Le fichier est prêt pour l'import !
Utilisez l'interface principale pour confirmer l'import.`;

            } catch (error) {
                result.className = 'result error';
                result.textContent = `❌ Erreur test import Excel: ${error.message}`;
            }
        }

        // Test import legacy
        function testLegacyImport() {
            const result = document.getElementById('legacyResult');
            result.className = 'result info';
            result.textContent = `Import CSV/JSON disponible dans l'interface principale.
            
Cliquez sur "Importer" puis choisissez CSV ou JSON.
Cette fonctionnalité existait déjà et fonctionne parfaitement.`;
        }

        // Télécharger un modèle Excel
        function downloadSampleExcel() {
            // Créer un fichier Excel simple avec les headers
            const sampleData = [
                ['Code Client', 'Nom', 'Prénom', 'Email', 'Téléphone', 'Mobile 1', 'Mobile 2', 'Adresse', 'Ville', 'Code Postal'],
                ['', 'DUPONT', 'Jean', '<EMAIL>', '0123456789', '0612345678', '', '123 Rue de la Paix', 'Paris', '75001'],
                ['', 'MARTIN', 'Marie', '<EMAIL>', '0234567890', '0623456789', '', '456 Avenue des Champs', 'Lyon', '69001'],
                ['', 'BERNARD', 'Pierre', '<EMAIL>', '0345678901', '0634567890', '', '789 Boulevard Saint-Michel', 'Marseille', '13001']
            ];
            
            // Convertir en CSV pour simplicité
            const csvContent = sampleData.map(row => row.map(cell => `"${cell}"`).join(',')).join('\n');
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            
            link.setAttribute('href', url);
            link.setAttribute('download', 'modele_import_clients.csv');
            link.style.visibility = 'hidden';
            
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            const result = document.getElementById('excelResult');
            result.className = 'result success';
            result.textContent = `✅ Modèle CSV téléchargé !
            
Vous pouvez ouvrir ce fichier dans Excel, le modifier, 
puis le sauvegarder au format .xlsx pour l'importer.`;
        }

        // Drag & Drop pour la zone Excel
        const excelZone = document.getElementById('excelUploadZone');
        
        excelZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            excelZone.classList.add('dragover');
        });
        
        excelZone.addEventListener('dragleave', (e) => {
            e.preventDefault();
            excelZone.classList.remove('dragover');
        });
        
        excelZone.addEventListener('drop', (e) => {
            e.preventDefault();
            excelZone.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                const file = files[0];
                if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
                    handleExcelTest({ target: { files: [file] } });
                } else {
                    const result = document.getElementById('excelResult');
                    result.className = 'result error';
                    result.textContent = '❌ Veuillez déposer un fichier Excel (.xlsx ou .xls)';
                }
            }
        });

        // Auto-start
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🧪 Page de test import Excel chargée');
        });
    </script>
</body>
</html>
