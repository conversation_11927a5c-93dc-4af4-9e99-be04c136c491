<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Interface</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        .result { margin: 10px 0; padding: 10px; background: #f0f0f0; }
    </style>
</head>
<body>
    <h1>🧪 Test Interface Clients</h1>
    
    <div class="test-section">
        <h3>1. Test API Références</h3>
        <button onclick="testReferences()">Tester Références</button>
        <div id="referencesResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>2. Test Clients</h3>
        <button onclick="testClients()">Tester Clients</button>
        <div id="clientsResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. Test Ajout Client</h3>
        <button onclick="testAddClient()">Ajouter Client Test</button>
        <div id="addResult" class="result"></div>
    </div>

    <script>
        async function testReferences() {
            try {
                const response = await fetch('/api/references/all');
                const data = await response.json();
                document.getElementById('referencesResult').innerHTML = 
                    `✅ Références chargées:<br>
                     - Nationalités: ${data.nationalities?.length || 0}<br>
                     - Sexes: ${data.sexes?.length || 0}<br>
                     - Situations familiales: ${data.situationsFamiliales?.length || 0}<br>
                     - Pays: ${data.paysNaissance?.length || 0}`;
            } catch (error) {
                document.getElementById('referencesResult').innerHTML = 
                    `❌ Erreur: ${error.message}`;
            }
        }

        async function testClients() {
            try {
                const response = await fetch('/api/clients');
                const data = await response.json();
                document.getElementById('clientsResult').innerHTML = 
                    `✅ ${data.length} clients trouvés`;
            } catch (error) {
                document.getElementById('clientsResult').innerHTML = 
                    `❌ Erreur: ${error.message}`;
            }
        }

        async function testAddClient() {
            const clientData = {
                nom: 'TEST',
                prenom: 'Interface',
                email: '<EMAIL>',
                telephone: '0123456789',
                nationality_id: 1,
                sexe_id: 1
            };

            try {
                const response = await fetch('/api/clients', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(clientData)
                });

                if (!response.ok) {
                    const error = await response.text();
                    throw new Error(error);
                }

                const result = await response.json();
                document.getElementById('addResult').innerHTML = 
                    `✅ Client ajouté avec ID: ${result.id}`;
            } catch (error) {
                document.getElementById('addResult').innerHTML = 
                    `❌ Erreur: ${error.message}`;
            }
        }
    </script>
</body>
</html>
