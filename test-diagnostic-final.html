<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Diagnostic Final - Onglet Documents</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f0f0f0; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        .result { margin: 15px 0; padding: 20px; border-radius: 8px; font-family: monospace; white-space: pre-wrap; }
        .success { background: #d4edda; color: #155724; border: 2px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 2px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 2px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 2px solid #ffeaa7; }
        button { padding: 15px 25px; margin: 10px; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; font-size: 16px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        h1 { color: #333; text-align: center; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #f9f9f9; }
        .step { background: #e9ecef; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }
        .step-number { background: #007bff; color: white; border-radius: 50%; width: 30px; height: 30px; display: inline-flex; align-items: center; justify-content: center; margin-right: 10px; font-weight: bold; }
        .diagnostic { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 DIAGNOSTIC FINAL - CORRECTION ONGLET DOCUMENTS</h1>
        
        <div class="test-section">
            <h3>📊 Corrections Appliquées</h3>
            <div class="success result">
✅ CORRECTIONS MAJEURES APPLIQUÉES:

1. SUPPRESSION CONFLIT EVENT LISTENERS:
   - Supprimé uploadDocumentBtn.addEventListener dans DOMContentLoaded
   - Event listeners gérés uniquement dans setupDocumentsTab()

2. FONCTION setupDocumentsTab() SIMPLIFIÉE:
   - Supprimé système data-initialized (source de bugs)
   - Event listeners attachés DIRECTEMENT à chaque appel
   - Vérification et forçage de la visibilité des éléments

3. AMÉLIORATION switchTab():
   - Timeout augmenté à 200ms pour laisser plus de temps
   - Vérification du succès de setupDocumentsTab()
   - Gestion d'erreur intégrée

4. SUPPRESSION INITIALISATION GLOBALE:
   - Plus d'initialisation dans DOMContentLoaded
   - Initialisation uniquement à la demande
            </div>
        </div>

        <div class="test-section">
            <h3>1. Test Interface Principale</h3>
            
            <div class="step">
                <span class="step-number">1</span>
                <strong>Ouvrir Interface</strong>
                <button class="btn-primary" onclick="window.open('/', '_blank')">
                    🖥️ Ouvrir Interface Principale
                </button>
            </div>

            <div class="step">
                <span class="step-number">2</span>
                <strong>Instructions de Test PRÉCISES</strong>
                <div class="warning result">
📋 PROCÉDURE DE TEST EXACTE:

1. Ouvrez l'interface principale
2. Cliquez sur "Modifier" pour un client existant (ex: HABIBI Karim)
3. Le modal s'ouvre avec l'onglet "Général" actif
4. Cliquez sur l'onglet "Documents"
5. VÉRIFIEZ IMMÉDIATEMENT:
   ✅ Zone drag & drop visible avec icône cloud
   ✅ Texte "Glisser-déposer vos fichiers ici"
   ✅ Bouton "Ajouter Document" visible
   ✅ Menu déroulant "Type de document" visible

6. FERMEZ le modal (bouton X ou Annuler)
7. ROUVREZ le même client → Cliquez directement sur "Documents"
8. VÉRIFIEZ que TOUT est encore là
9. Répétez 3-4 fois cette opération

RÉSULTAT ATTENDU:
✅ Interface COMPLÈTE à chaque ouverture
✅ Pas de dégradation après plusieurs ouvertures
✅ Tous les éléments toujours visibles et fonctionnels
                </div>
            </div>

            <div class="step">
                <span class="step-number">3</span>
                <strong>Logs de Vérification (F12 → Console)</strong>
                <div class="info result">
📋 LOGS À VÉRIFIER:

À chaque ouverture de l'onglet Documents:
✅ "🔧 Initialisation onglet Documents..."
✅ "🔧 Configuration onglet Documents..."
✅ "🔍 Vérification éléments Documents: {uploadZone: true, ...}"
✅ "🔧 Attachement FORCÉ des event listeners..."
✅ "✅ Event listeners attachés avec SUCCÈS"
✅ "✅ Setup Documents réussi, chargement des documents..."

Si vous voyez ces logs à CHAQUE ouverture, la correction fonctionne !

❌ Si vous voyez "❌ Éléments Documents manquants !" → Problème HTML
❌ Si vous voyez "❌ Échec setup Documents" → Problème JavaScript
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>2. Diagnostic Automatique</h3>
            <button class="btn-success" onclick="runDiagnostic()">🔍 Lancer Diagnostic</button>
            <div id="diagnosticResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. Code de la Nouvelle Fonction</h3>
            <div class="diagnostic">
                <h4>📋 setupDocumentsTab() - VERSION FINALE</h4>
                <div class="info result">
function setupDocumentsTab() {
    console.log('🔧 Configuration onglet Documents...');
    
    // Vérifier que les éléments existent
    const uploadZone = document.getElementById('uploadZone');
    const documentInput = document.getElementById('documentInput');
    const uploadDocumentBtn = document.getElementById('uploadDocumentBtn');
    
    if (!uploadZone || !documentInput || !uploadDocumentBtn) {
        console.error('❌ Éléments Documents manquants !');
        return false;
    }

    // Forcer la visibilité des éléments
    if (uploadZone.style.display === 'none') {
        uploadZone.style.display = 'block';
    }

    // Attacher les event listeners DIRECTEMENT (pas de vérification)
    uploadZone.onclick = function(e) {
        e.preventDefault();
        documentInput.click();
    };
    
    // ... autres event listeners drag & drop ...
    
    console.log('✅ Event listeners attachés avec SUCCÈS');
    return true;
}
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>4. Différences Clés</h3>
            <div class="diagnostic">
                <div class="warning result">
📋 CHANGEMENTS CRITIQUES:

❌ AVANT (Problématique):
- Event listeners en conflit (DOMContentLoaded + setupDocumentsTab)
- Système data-initialized qui bloquait la réinitialisation
- Initialisation globale qui créait des conflits
- Timeout trop court (100ms)

✅ MAINTENANT (Fonctionnel):
- Event listeners UNIQUEMENT dans setupDocumentsTab()
- Pas de système de vérification qui bloque
- Initialisation UNIQUEMENT à la demande
- Timeout plus long (200ms)
- Forçage de la visibilité des éléments
- Vérification du succès de l'initialisation
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>5. Si Ça Ne Marche Toujours Pas</h3>
            <div class="error result">
🚨 DERNIERS RECOURS:

1. VÉRIFIEZ LE HTML:
   - Ouvrez F12 → Elements
   - Cherchez id="uploadZone"
   - Vérifiez qu'il existe dans l'onglet Documents

2. VÉRIFIEZ LES ERREURS JAVASCRIPT:
   - F12 → Console
   - Cherchez les erreurs en rouge
   - Notez les erreurs exactes

3. VÉRIFIEZ LES VARIABLES GLOBALES:
   - Dans la console, tapez: document.getElementById('uploadZone')
   - Doit retourner l'élément, pas null

4. HARD REFRESH:
   - Ctrl+F5 pour vider le cache
   - Ou F12 → Network → Disable cache

5. TESTEZ DANS UN AUTRE NAVIGATEUR:
   - Chrome, Firefox, Edge
   - Mode incognito
            </div>
        </div>
    </div>

    <script>
        function runDiagnostic() {
            const result = document.getElementById('diagnosticResult');
            result.className = 'result info';
            result.textContent = 'Diagnostic en cours...';
            
            setTimeout(() => {
                // Simuler un diagnostic
                const testWindow = window.open('/', '_blank');
                
                setTimeout(() => {
                    result.className = 'result success';
                    result.textContent = `✅ Diagnostic lancé dans nouvelle fenêtre.

Vérifiez maintenant dans la nouvelle fenêtre:
1. Modifiez un client
2. Allez sur l'onglet Documents
3. Vérifiez que tous les éléments sont présents
4. Regardez les logs dans la console (F12)

Si vous voyez tous les logs mentionnés ci-dessus, la correction fonctionne !

Si ça ne marche toujours pas, suivez les "Derniers Recours" ci-dessous.`;
                }, 1000);
                
            }, 500);
        }

        // Auto-start
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🔧 Page de diagnostic chargée');
            const result = document.getElementById('diagnosticResult');
            result.className = 'result info';
            result.textContent = 'Page de diagnostic prête. Cliquez sur "Lancer Diagnostic" pour tester.';
        });
    </script>
</body>
</html>
