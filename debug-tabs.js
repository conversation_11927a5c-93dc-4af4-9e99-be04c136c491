// Script de débogage pour les onglets
console.log('🔍 Démarrage du débogage des onglets...');

// Attendre que le DOM soit chargé
document.addEventListener('DOMContentLoaded', () => {
    console.log('📋 DOM chargé, vérification des éléments...');
    
    // Vérifier les boutons d'onglets
    const tabButtons = document.querySelectorAll('.tab-btn');
    console.log('🔘 Boutons d\'onglets trouvés:', tabButtons.length);
    
    tabButtons.forEach((btn, index) => {
        console.log(`  - Bouton ${index + 1}: ${btn.dataset.tab} (${btn.textContent.trim()})`);
    });
    
    // Vérifier les contenus d'onglets
    const tabContents = document.querySelectorAll('.tab-content');
    console.log('📄 Contenus d\'onglets trouvés:', tabContents.length);
    
    tabContents.forEach((content, index) => {
        console.log(`  - Contenu ${index + 1}: ${content.id} (visible: ${content.style.display !== 'none'})`);
    });
    
    // Vérifier le modal
    const modal = document.getElementById('clientModal');
    console.log('🪟 Modal trouvé:', !!modal);
    
    // Vérifier le formulaire
    const form = document.getElementById('clientForm');
    console.log('📝 Formulaire trouvé:', !!form);
    
    // Ajouter des event listeners de test
    tabButtons.forEach(btn => {
        btn.addEventListener('click', (e) => {
            console.log('🖱️ Clic détecté sur onglet:', btn.dataset.tab);
        });
    });
    
    // Test d'ouverture du modal
    const addBtn = document.getElementById('addClientBtn');
    if (addBtn) {
        console.log('➕ Bouton d\'ajout trouvé');
        addBtn.addEventListener('click', () => {
            console.log('🖱️ Clic sur bouton d\'ajout détecté');
        });
    }
    
    console.log('✅ Débogage terminé');
});
