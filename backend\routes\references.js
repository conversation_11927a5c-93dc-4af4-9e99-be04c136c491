const express = require('express');
const router = express.Router();
const clientsDB = require('../database');

// GET /api/references/nationalities - Obtenir toutes les nationalités
router.get('/nationalities', (req, res) => {
    clientsDB.getAllNationalities((err, nationalities) => {
        if (err) {
            return res.status(500).json({ error: 'Erreur lors de la récupération des nationalités' });
        }
        res.json(nationalities);
    });
});

// GET /api/references/sexes - Obtenir tous les sexes
router.get('/sexes', (req, res) => {
    clientsDB.getAllSexes((err, sexes) => {
        if (err) {
            return res.status(500).json({ error: 'Erreur lors de la récupération des sexes' });
        }
        res.json(sexes);
    });
});

// GET /api/references/situations-familiales - Obtenir toutes les situations familiales
router.get('/situations-familiales', (req, res) => {
    clientsDB.getAllSituationsFamiliales((err, situations) => {
        if (err) {
            return res.status(500).json({ error: 'Erreur lors de la récupération des situations familiales' });
        }
        res.json(situations);
    });
});

// GET /api/references/pays-naissance - Obtenir tous les pays de naissance
router.get('/pays-naissance', (req, res) => {
    clientsDB.getAllPaysDeNaissance((err, pays) => {
        if (err) {
            return res.status(500).json({ error: 'Erreur lors de la récupération des pays de naissance' });
        }
        res.json(pays);
    });
});

// GET /api/references/lieux-naissance - Obtenir tous les lieux de naissance
router.get('/lieux-naissance', (req, res) => {
    clientsDB.getAllLieuxDeNaissance((err, lieux) => {
        if (err) {
            return res.status(500).json({ error: 'Erreur lors de la récupération des lieux de naissance' });
        }
        res.json(lieux);
    });
});

// GET /api/references/labels-tel - Obtenir tous les labels téléphone
router.get('/labels-tel', (req, res) => {
    clientsDB.getAllLabelsTel((err, labels) => {
        if (err) {
            return res.status(500).json({ error: 'Erreur lors de la récupération des labels téléphone' });
        }
        res.json(labels);
    });
});

// GET /api/references/labels-mobile - Obtenir tous les labels mobile
router.get('/labels-mobile', (req, res) => {
    clientsDB.getAllLabelsMobile((err, labels) => {
        if (err) {
            return res.status(500).json({ error: 'Erreur lors de la récupération des labels mobile' });
        }
        res.json(labels);
    });
});

// GET /api/references/airline-codes - Obtenir tous les codes compagnies aériennes
router.get('/airline-codes', (req, res) => {
    clientsDB.getAllAirlineCodes((err, codes) => {
        if (err) {
            return res.status(500).json({ error: 'Erreur lors de la récupération des codes compagnies' });
        }
        res.json(codes);
    });
});

// GET /api/references/labels-related-name - Obtenir tous les labels nom apparenté
router.get('/labels-related-name', (req, res) => {
    clientsDB.getAllLabelsRelatedName((err, labels) => {
        if (err) {
            return res.status(500).json({ error: 'Erreur lors de la récupération des labels nom apparenté' });
        }
        res.json(labels);
    });
});

// GET /api/references/comptes-comptables - Obtenir tous les comptes comptables
router.get('/comptes-comptables', (req, res) => {
    clientsDB.getAllComptesComptables((err, comptes) => {
        if (err) {
            return res.status(500).json({ error: 'Erreur lors de la récupération des comptes comptables' });
        }
        res.json(comptes);
    });
});

// GET /api/references/all - Obtenir toutes les données de référence en une seule requête
router.get('/all', async (req, res) => {
    try {
        const references = {};
        
        // Utiliser des promesses pour récupérer toutes les données en parallèle
        const promises = [
            new Promise((resolve, reject) => clientsDB.getAllNationalities((err, data) => err ? reject(err) : resolve(['nationalities', data]))),
            new Promise((resolve, reject) => clientsDB.getAllSexes((err, data) => err ? reject(err) : resolve(['sexes', data]))),
            new Promise((resolve, reject) => clientsDB.getAllSituationsFamiliales((err, data) => err ? reject(err) : resolve(['situationsFamiliales', data]))),
            new Promise((resolve, reject) => clientsDB.getAllPaysDeNaissance((err, data) => err ? reject(err) : resolve(['paysNaissance', data]))),
            new Promise((resolve, reject) => clientsDB.getAllLieuxDeNaissance((err, data) => err ? reject(err) : resolve(['lieuxNaissance', data]))),
            new Promise((resolve, reject) => clientsDB.getAllLabelsTel((err, data) => err ? reject(err) : resolve(['labelsTel', data]))),
            new Promise((resolve, reject) => clientsDB.getAllLabelsMobile((err, data) => err ? reject(err) : resolve(['labelsMobile', data]))),
            new Promise((resolve, reject) => clientsDB.getAllAirlineCodes((err, data) => err ? reject(err) : resolve(['airlineCodes', data]))),
            new Promise((resolve, reject) => clientsDB.getAllLabelsRelatedName((err, data) => err ? reject(err) : resolve(['labelsRelatedName', data]))),
            new Promise((resolve, reject) => clientsDB.getAllComptesComptables((err, data) => err ? reject(err) : resolve(['comptesComptables', data])))
        ];
        
        const results = await Promise.all(promises);
        
        // Organiser les résultats
        results.forEach(([key, data]) => {
            references[key] = data;
        });
        
        res.json(references);
    } catch (error) {
        console.error('Erreur récupération références:', error);
        res.status(500).json({ error: 'Erreur lors de la récupération des données de référence' });
    }
});

module.exports = router;
