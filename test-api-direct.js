// Test direct de l'API sans dépendances externes
const http = require('http');

console.log('🧪 Test direct de l\'API...');

// Test GET /api/clients
function testGetClients() {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'localhost',
            port: 3000,
            path: '/api/clients',
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        };

        const req = http.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                console.log(`📥 GET /api/clients - Status: ${res.statusCode}`);
                console.log(`📋 Response: ${data}`);
                
                if (res.statusCode === 200) {
                    try {
                        const clients = JSON.parse(data);
                        console.log(`✅ ${clients.length} clients trouvés`);
                        resolve(clients);
                    } catch (error) {
                        console.log(`❌ Erreur parsing JSON: ${error.message}`);
                        reject(error);
                    }
                } else {
                    console.log(`❌ Erreur HTTP: ${res.statusCode}`);
                    reject(new Error(`HTTP ${res.statusCode}: ${data}`));
                }
            });
        });

        req.on('error', (error) => {
            console.log(`❌ Erreur requête: ${error.message}`);
            reject(error);
        });

        req.end();
    });
}

// Test POST /api/clients
function testAddClient() {
    return new Promise((resolve, reject) => {
        const clientData = {
            nom: 'TEST_DIRECT',
            prenom: 'Client',
            email: `test.direct.${Date.now()}@example.com`,
            telephone: '0123456789'
        };

        const postData = JSON.stringify(clientData);
        
        const options = {
            hostname: 'localhost',
            port: 3000,
            path: '/api/clients',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            }
        };

        console.log(`📤 POST /api/clients`);
        console.log(`📋 Données: ${postData}`);

        const req = http.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                console.log(`📥 POST Response - Status: ${res.statusCode}`);
                console.log(`📋 Response: ${data}`);
                
                if (res.statusCode === 200 || res.statusCode === 201) {
                    try {
                        const result = JSON.parse(data);
                        console.log(`✅ Client ajouté avec ID: ${result.id}`);
                        resolve(result);
                    } catch (error) {
                        console.log(`❌ Erreur parsing JSON: ${error.message}`);
                        reject(error);
                    }
                } else {
                    console.log(`❌ Erreur HTTP: ${res.statusCode}`);
                    console.log(`📋 Détails: ${data}`);
                    reject(new Error(`HTTP ${res.statusCode}: ${data}`));
                }
            });
        });

        req.on('error', (error) => {
            console.log(`❌ Erreur requête: ${error.message}`);
            reject(error);
        });

        req.write(postData);
        req.end();
    });
}

// Exécuter les tests
async function runTests() {
    try {
        console.log('🚀 Démarrage des tests...\n');
        
        // Test 1: Vérifier l'API GET
        console.log('📋 Test 1: GET /api/clients');
        await testGetClients();
        console.log('');
        
        // Test 2: Ajouter un client
        console.log('➕ Test 2: POST /api/clients');
        await testAddClient();
        console.log('');
        
        // Test 3: Vérifier que le client a été ajouté
        console.log('🔍 Test 3: Vérification après ajout');
        await testGetClients();
        
        console.log('\n🎉 TOUS LES TESTS RÉUSSIS !');
        
    } catch (error) {
        console.log(`\n❌ ÉCHEC DES TESTS: ${error.message}`);
        process.exit(1);
    }
}

// Lancer les tests
runTests();
