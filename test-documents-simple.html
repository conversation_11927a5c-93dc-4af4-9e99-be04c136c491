<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Test Documents - Correction Simple</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f0f0f0; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        .result { margin: 15px 0; padding: 20px; border-radius: 8px; font-family: monospace; white-space: pre-wrap; }
        .success { background: #d4edda; color: #155724; border: 2px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 2px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 2px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 2px solid #ffeaa7; }
        button { padding: 15px 25px; margin: 10px; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; font-size: 16px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        h1 { color: #333; text-align: center; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #f9f9f9; }
        .step { background: #e9ecef; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }
        .step-number { background: #007bff; color: white; border-radius: 50%; width: 30px; height: 30px; display: inline-flex; align-items: center; justify-content: center; margin-right: 10px; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Test Documents - Nouvelle Approche Simple</h1>
        
        <div class="test-section">
            <h3>📊 Correction Appliquée</h3>
            <div class="success result">
✅ NOUVELLE APPROCHE SIMPLE:

1. Fonction setupDocumentsTab() remplace l'ancienne approche complexe
2. Utilisation d'attribut data-initialized pour éviter les doublons
3. Event listeners attachés avec onclick/ondragover/ondrop (plus simple)
4. Initialisation dans DOMContentLoaded + à chaque ouverture d'onglet
5. Pas de clonage d'éléments (source de problèmes)
            </div>
        </div>

        <div class="test-section">
            <h3>1. Test Interface Principale</h3>
            
            <div class="step">
                <span class="step-number">1</span>
                <strong>Ouvrir Interface</strong>
                <button class="btn-primary" onclick="window.open('/', '_blank')">
                    🖥️ Ouvrir Interface Principale
                </button>
            </div>

            <div class="step">
                <span class="step-number">2</span>
                <strong>Test Répétitif</strong>
                <div class="warning result">
📋 INSTRUCTIONS DE TEST:

1. Ouvrez l'interface principale
2. Cliquez sur "Modifier" pour un client existant
3. Allez sur l'onglet "Documents"
4. Vérifiez que vous voyez:
   ✅ Zone drag & drop avec icône cloud
   ✅ Texte "Glisser-déposer vos fichiers ici"
   ✅ Bouton "Ajouter Document"
   ✅ Menu déroulant des types de documents

5. FERMEZ le modal (bouton X ou Annuler)
6. ROUVREZ le même client → Onglet Documents
7. Vérifiez que TOUT est encore là
8. Répétez 3-4 fois cette opération

RÉSULTAT ATTENDU:
✅ Interface TOUJOURS complète
✅ Pas de dégradation après plusieurs ouvertures
                </div>
            </div>

            <div class="step">
                <span class="step-number">3</span>
                <strong>Vérification Console</strong>
                <div class="info result">
📋 LOGS À VÉRIFIER (F12 → Console):

À chaque ouverture de l'onglet Documents:
✅ "🔧 Initialisation onglet Documents..."
✅ "🔧 Configuration onglet Documents..."
✅ "✅ Tous les éléments Documents trouvés"
✅ "🔧 Attachement des event listeners..." (première fois)
✅ "ℹ️ Onglet Documents déjà initialisé" (fois suivantes)

Si vous voyez ces logs, la correction fonctionne !
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>2. Test Automatique</h3>
            <button class="btn-success" onclick="testDocumentsElements()">🧪 Tester Éléments Documents</button>
            <div id="testResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. Différences avec l'Ancienne Approche</h3>
            <div class="info result">
📋 CHANGEMENTS TECHNIQUES:

❌ ANCIENNE APPROCHE (problématique):
- Clonage des éléments DOM (source de bugs)
- Event listeners multiples accumulés
- Réinitialisation complexe à chaque fois

✅ NOUVELLE APPROCHE (simple et robuste):
- Pas de clonage d'éléments
- Vérification data-initialized pour éviter doublons
- Event listeners simples (onclick, ondragover, etc.)
- Initialisation une seule fois + vérification à chaque ouverture
- Code plus simple et plus fiable
            </div>
        </div>

        <div class="test-section">
            <h3>4. Code de la Nouvelle Fonction</h3>
            <div class="info result">
📋 FONCTION setupDocumentsTab():

function setupDocumentsTab() {
    console.log('🔧 Configuration onglet Documents...');
    
    const uploadZone = document.getElementById('uploadZone');
    const documentInput = document.getElementById('documentInput');
    const uploadDocumentBtn = document.getElementById('uploadDocumentBtn');
    
    if (!uploadZone || !documentInput || !uploadDocumentBtn) {
        console.error('❌ Éléments Documents non trouvés');
        return;
    }

    // Vérifier si déjà initialisé
    if (!uploadZone.hasAttribute('data-initialized')) {
        // Marquer comme initialisé
        uploadZone.setAttribute('data-initialized', 'true');
        
        // Attacher les event listeners (une seule fois)
        uploadZone.onclick = function() { documentInput.click(); };
        uploadZone.ondragover = function(e) { /* drag & drop */ };
        // ... autres event listeners
        
        console.log('✅ Event listeners attachés');
    } else {
        console.log('ℹ️ Déjà initialisé');
    }
}
            </div>
        </div>
    </div>

    <script>
        function testDocumentsElements() {
            const result = document.getElementById('testResult');
            
            // Test si on peut accéder aux éléments Documents
            try {
                // Simuler l'ouverture d'une nouvelle fenêtre et test des éléments
                const testWindow = window.open('/', '_blank');
                
                setTimeout(() => {
                    result.className = 'result info';
                    result.textContent = `Test lancé dans nouvelle fenêtre.
                    
Vérifiez dans la nouvelle fenêtre:
1. Ouvrez un client en modification
2. Allez sur l'onglet Documents
3. Vérifiez que tous les éléments sont présents
4. Fermez et rouvrez plusieurs fois

Si tout fonctionne, la correction est réussie !`;
                }, 1000);
                
            } catch (error) {
                result.className = 'result error';
                result.textContent = `Erreur lors du test: ${error.message}`;
            }
        }

        // Auto-start
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🔧 Page de test chargée');
            const result = document.getElementById('testResult');
            result.className = 'result info';
            result.textContent = 'Page de test prête. Utilisez les boutons pour tester.';
        });
    </script>
</body>
</html>
