<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔄 Test Rechargement Documents</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f0f0f0; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        .result { margin: 15px 0; padding: 20px; border-radius: 8px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto; }
        .success { background: #d4edda; color: #155724; border: 2px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 2px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 2px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 2px solid #ffeaa7; }
        button { padding: 15px 25px; margin: 10px; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; font-size: 16px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        h1 { color: #333; text-align: center; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #f9f9f9; }
        .client-list { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; max-height: 200px; overflow-y: auto; }
        .client-item { padding: 10px; margin: 5px 0; background: white; border-radius: 5px; border: 1px solid #ddd; cursor: pointer; }
        .client-item:hover { background: #e9ecef; }
        .client-item.selected { background: #007bff; color: white; }
        .document-list { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .document-item { padding: 15px; margin: 10px 0; background: white; border-radius: 5px; border: 1px solid #ddd; display: flex; justify-content: space-between; align-items: center; }
        .document-info { flex: 1; }
        .document-actions { display: flex; gap: 10px; }
        .upload-zone { border: 2px dashed #ddd; padding: 30px; text-align: center; border-radius: 8px; margin: 15px 0; cursor: pointer; }
        .upload-zone:hover { border-color: #007bff; background: rgba(0, 123, 255, 0.1); }
        .step { background: #e9ecef; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }
        .step-number { background: #007bff; color: white; border-radius: 50%; width: 30px; height: 30px; display: inline-flex; align-items: center; justify-content: center; margin-right: 10px; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Test Rechargement Documents - Correction Appliquée</h1>
        
        <div class="test-section">
            <h3>📊 Problème Identifié et Corrigé</h3>
            <div class="success result">
✅ PROBLÈME: Documents disparaissent quand on revient sur la fiche client
✅ CAUSE: loadClientDocuments() non appelée lors de l'ouverture du modal
✅ SOLUTION: Ajout de await loadClientDocuments(id) dans editClient()
✅ AMÉLIORATION: Chargement automatique des documents à l'ouverture
            </div>
        </div>

        <div class="test-section">
            <h3>1. Sélection du Client</h3>
            <button class="btn-primary" onclick="loadClients()">📋 Charger Clients</button>
            <div id="clientsList" class="client-list"></div>
            <div id="selectedClient" class="info result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>2. Test Cycle Complet de Rechargement</h3>
            <div id="testSection" style="display: none;">
                
                <!-- Étape 1: Upload initial -->
                <div class="step">
                    <span class="step-number">1</span>
                    <strong>Upload Initial de Documents</strong>
                    <div class="upload-zone" id="uploadZone">
                        <i class="fas fa-cloud-upload-alt" style="font-size: 2rem; color: #6c757d; margin-bottom: 10px;"></i>
                        <h5>Glisser-déposer un fichier ici</h5>
                        <input type="file" id="documentInput" accept=".pdf,.jpg,.jpeg,.png,.gif" style="margin: 10px 0;">
                    </div>
                    <select id="documentType" style="padding: 8px; margin: 5px; border-radius: 4px;">
                        <option value="passeport">🛂 Passeport</option>
                        <option value="carte_identite">🆔 Carte d'identité</option>
                        <option value="autre">📄 Autre document</option>
                    </select>
                    <button class="btn-success" onclick="uploadDocument()">📤 Upload</button>
                </div>

                <!-- Étape 2: Vérification documents -->
                <div class="step">
                    <span class="step-number">2</span>
                    <strong>Vérification Documents Uploadés</strong>
                    <button class="btn-primary" onclick="loadDocuments()">🔄 Actualiser Liste</button>
                    <div id="documentsList" class="document-list"></div>
                </div>

                <!-- Étape 3: Test rechargement -->
                <div class="step">
                    <span class="step-number">3</span>
                    <strong>Test Rechargement (SIMULATION)</strong>
                    <p>Simulation de retour sur la fiche client :</p>
                    <button class="btn-warning" onclick="simulateClientReload()">🔄 Simuler Rechargement Client</button>
                    <div id="reloadResult" class="result"></div>
                </div>

                <!-- Étape 4: Instructions test réel -->
                <div class="step">
                    <span class="step-number">4</span>
                    <strong>Test Réel dans l'Interface</strong>
                    <div class="warning result">
📋 INSTRUCTIONS POUR TEST COMPLET:

1. Uploadez un document ici (étape 1)
2. Vérifiez qu'il apparaît (étape 2)
3. Ouvrez l'interface principale (bouton ci-dessous)
4. Modifiez le même client → Onglet Documents
5. Vérifiez que les documents sont VISIBLES immédiatement
6. Fermez le modal et rouvrez-le
7. Vérifiez à nouveau que les documents sont toujours là

RÉSULTAT ATTENDU:
✅ Documents visibles dès l'ouverture du modal
✅ Pas besoin de recharger localhost:3000
✅ Documents persistent entre les ouvertures/fermetures
                    </div>
                    <button class="btn-primary" onclick="window.open('/', '_blank')" style="padding: 20px 30px; font-size: 18px;">
                        🖥️ Ouvrir Interface Principale
                    </button>
                </div>
            </div>
            <div id="testResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. Vérification Technique</h3>
            <div class="info result">
📋 VÉRIFICATIONS DANS LA CONSOLE (F12):

Lors de l'ouverture d'un client en modification, vous devriez voir :
✅ "📄 Chargement des documents pour le client: X"
✅ "📋 Documents récupérés: Y"
✅ "📄 Affichage de Y documents"
✅ "🔗 Attachement des event listeners pour les documents..."

Si ces logs apparaissent, la correction fonctionne !
            </div>
        </div>
    </div>

    <script>
        let selectedClientId = null;
        let clientDocuments = [];

        // Charger les clients
        async function loadClients() {
            try {
                const response = await fetch('/api/clients');
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                
                const clients = await response.json();
                
                const clientsList = document.getElementById('clientsList');
                clientsList.innerHTML = clients.map(client => `
                    <div class="client-item" onclick="selectClient(${client.id}, '${client.nom}', '${client.prenom}')">
                        <strong>${client.nom} ${client.prenom}</strong><br>
                        <small>${client.email}</small>
                    </div>
                `).join('');
                
                addResult('testResult', 'success', `${clients.length} clients chargés`);
                
            } catch (error) {
                addResult('testResult', 'error', `Erreur: ${error.message}`);
            }
        }

        // Sélectionner un client
        function selectClient(clientId, nom, prenom) {
            selectedClientId = clientId;
            
            document.querySelectorAll('.client-item').forEach(item => {
                item.classList.remove('selected');
            });
            event.target.classList.add('selected');
            
            const selectedDiv = document.getElementById('selectedClient');
            selectedDiv.style.display = 'block';
            selectedDiv.textContent = `Client sélectionné: ${nom} ${prenom} (ID: ${clientId})`;
            selectedDiv.className = 'success result';
            
            document.getElementById('testSection').style.display = 'block';
            
            initializeDragDrop();
            loadDocuments();
        }

        // Initialiser drag & drop
        function initializeDragDrop() {
            const uploadZone = document.getElementById('uploadZone');
            const documentInput = document.getElementById('documentInput');
            
            uploadZone.onclick = () => documentInput.click();
            
            uploadZone.ondragover = (e) => {
                e.preventDefault();
                uploadZone.style.borderColor = '#007bff';
                uploadZone.style.background = 'rgba(0, 123, 255, 0.1)';
            };
            
            uploadZone.ondragleave = (e) => {
                e.preventDefault();
                uploadZone.style.borderColor = '#ddd';
                uploadZone.style.background = '';
            };
            
            uploadZone.ondrop = (e) => {
                e.preventDefault();
                uploadZone.style.borderColor = '#ddd';
                uploadZone.style.background = '';
                documentInput.files = e.dataTransfer.files;
                if (documentInput.files.length > 0) {
                    uploadDocument();
                }
            };
        }

        // Upload document
        async function uploadDocument() {
            if (!selectedClientId) {
                addResult('testResult', 'error', 'Aucun client sélectionné');
                return;
            }

            const files = document.getElementById('documentInput').files;
            if (!files || files.length === 0) {
                addResult('testResult', 'error', 'Aucun fichier sélectionné');
                return;
            }

            try {
                addResult('testResult', 'info', `Upload de ${files.length} fichier(s)...`);
                
                for (let i = 0; i < files.length; i++) {
                    const file = files[i];
                    const formData = new FormData();
                    formData.append('document', file);
                    formData.append('type_document', document.getElementById('documentType').value);

                    const response = await fetch(`/api/upload/document/${selectedClientId}`, {
                        method: 'POST',
                        body: formData
                    });

                    if (!response.ok) {
                        const error = await response.json();
                        throw new Error(error.error || 'Erreur upload');
                    }
                }

                addResult('testResult', 'success', `✅ Upload réussi! Documents sauvegardés en base`);
                document.getElementById('documentInput').value = '';
                
                // Recharger et attacher les event listeners
                await loadDocuments();
                
            } catch (error) {
                addResult('testResult', 'error', `❌ Erreur: ${error.message}`);
            }
        }

        // Charger les documents
        async function loadDocuments() {
            if (!selectedClientId) return;

            try {
                addResult('testResult', 'info', 'Chargement des documents depuis la base...');
                
                const response = await fetch(`/api/upload/documents/${selectedClientId}`);
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                
                clientDocuments = await response.json();
                
                const documentsList = document.getElementById('documentsList');
                if (clientDocuments.length === 0) {
                    documentsList.innerHTML = '<div style="text-align: center; color: #6c757d; padding: 20px;">Aucun document en base</div>';
                } else {
                    documentsList.innerHTML = clientDocuments.map(doc => `
                        <div class="document-item">
                            <div class="document-info">
                                <strong>${getDocumentTypeLabel(doc.type_document)}</strong><br>
                                <small>${doc.nom_fichier} (${formatFileSize(doc.taille_fichier)})</small><br>
                                <small>ID: ${doc.id} - Uploadé: ${new Date(doc.date_upload).toLocaleString()}</small>
                            </div>
                            <div class="document-actions">
                                <button class="btn-primary btn-download" data-doc-id="${doc.id}">⬇️ Télécharger</button>
                                <button class="btn-danger btn-delete" data-doc-id="${doc.id}">🗑️ Supprimer</button>
                            </div>
                        </div>
                    `).join('');
                    
                    // Attacher les event listeners
                    attachEventListeners();
                }
                
                addResult('testResult', 'success', `✅ ${clientDocuments.length} document(s) chargé(s) depuis la base`);
                
            } catch (error) {
                addResult('testResult', 'error', `❌ Erreur: ${error.message}`);
            }
        }

        // Simuler le rechargement du client (comme dans l'interface principale)
        async function simulateClientReload() {
            if (!selectedClientId) {
                addResult('reloadResult', 'error', 'Aucun client sélectionné');
                return;
            }

            try {
                addResult('reloadResult', 'info', 'Simulation du rechargement du client...');
                
                // Simuler ce qui se passe dans editClient()
                console.log('📄 Chargement des documents pour le client:', selectedClientId);
                
                const response = await fetch(`/api/upload/documents/${selectedClientId}`);
                if (!response.ok) throw new Error('Erreur lors du chargement des documents');

                const documents = await response.json();
                console.log('📋 Documents récupérés:', documents.length);
                
                if (documents.length > 0) {
                    addResult('reloadResult', 'success', `✅ SUCCÈS: ${documents.length} document(s) rechargé(s) automatiquement!`);
                } else {
                    addResult('reloadResult', 'warning', '⚠️ Aucun document trouvé pour ce client');
                }
                
            } catch (error) {
                addResult('reloadResult', 'error', `❌ Erreur simulation: ${error.message}`);
            }
        }

        // Attacher les event listeners
        function attachEventListeners() {
            // Boutons de téléchargement
            const downloadButtons = document.querySelectorAll('.btn-download');
            downloadButtons.forEach(button => {
                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    const docId = button.getAttribute('data-doc-id');
                    downloadDocument(parseInt(docId));
                });
            });

            // Boutons de suppression
            const deleteButtons = document.querySelectorAll('.btn-delete');
            deleteButtons.forEach(button => {
                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    const docId = button.getAttribute('data-doc-id');
                    deleteDocument(parseInt(docId));
                });
            });
        }

        // Télécharger document
        function downloadDocument(documentId) {
            try {
                const downloadUrl = `/api/upload/document/${documentId}/download`;
                const link = document.createElement('a');
                link.href = downloadUrl;
                link.target = '_blank';
                link.download = '';
                
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                addResult('testResult', 'success', `✅ Téléchargement initié pour document ${documentId}`);
                
            } catch (error) {
                addResult('testResult', 'error', `❌ Erreur: ${error.message}`);
            }
        }

        // Supprimer document
        async function deleteDocument(documentId) {
            if (!confirm('Supprimer ce document ?')) return;

            try {
                const response = await fetch(`/api/upload/document/${documentId}`, {
                    method: 'DELETE'
                });

                if (!response.ok) throw new Error(`HTTP ${response.status}`);

                addResult('testResult', 'success', 'Document supprimé');
                loadDocuments();
                
            } catch (error) {
                addResult('testResult', 'error', `Erreur: ${error.message}`);
            }
        }

        // Utilitaires
        function getDocumentTypeLabel(type) {
            const types = {
                'passeport': '🛂 Passeport',
                'carte_identite': '🆔 Carte d\'identité',
                'autre': '📄 Autre'
            };
            return types[type] || '📄 Document';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function addResult(containerId, type, message) {
            const container = document.getElementById(containerId);
            container.className = `result ${type}`;
            container.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
        }

        // Auto-start
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🔄 Page de test rechargement chargée');
            loadClients();
        });
    </script>
</body>
</html>
