<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 Test Automatique Interface</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f0f0f0; }
        .test-container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #f9f9f9; }
        .test-result { margin: 10px 0; padding: 15px; border-radius: 5px; font-family: monospace; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button { padding: 12px 24px; margin: 8px; border: none; border-radius: 6px; cursor: pointer; font-weight: bold; font-size: 14px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        .progress { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; margin: 10px 0; }
        .progress-bar { height: 100%; background: linear-gradient(90deg, #007bff, #28a745); transition: width 0.3s ease; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin: 20px 0; }
        .stat-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; text-align: center; }
        .stat-number { font-size: 2rem; font-weight: bold; }
        .stat-label { font-size: 0.9rem; opacity: 0.9; }
        h1 { color: #333; text-align: center; margin-bottom: 30px; }
        h3 { color: #495057; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🤖 Test Automatique - Interface Gestion Clients</h1>
        
        <!-- Statistiques en temps réel -->
        <div class="test-section">
            <h3>📊 Statistiques Actuelles</h3>
            <div class="stats-grid" id="statsGrid">
                <div class="stat-card">
                    <div class="stat-number" id="totalClients">-</div>
                    <div class="stat-label">Total Clients</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="thisMonth">-</div>
                    <div class="stat-label">Ce Mois</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="thisYear">-</div>
                    <div class="stat-label">Cette Année</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="averageAge">-</div>
                    <div class="stat-label">Âge Moyen</div>
                </div>
            </div>
            <button class="btn-primary" onclick="loadStats()">🔄 Actualiser Statistiques</button>
            <div id="statsResult" class="test-result"></div>
        </div>

        <!-- Tests automatiques -->
        <div class="test-section">
            <h3>🧪 Tests Automatiques</h3>
            <div class="progress">
                <div class="progress-bar" id="progressBar" style="width: 0%"></div>
            </div>
            <div style="text-align: center; margin: 20px 0;">
                <button class="btn-success" onclick="runAllTests()" id="runTestsBtn">🚀 Lancer Tous les Tests</button>
                <button class="btn-warning" onclick="runQuickTests()">⚡ Tests Rapides</button>
                <button class="btn-primary" onclick="testCRUD()">✏️ Test CRUD</button>
                <button class="btn-danger" onclick="clearResults()">🗑️ Effacer Résultats</button>
            </div>
            <div id="testResults"></div>
        </div>

        <!-- Test des fonctionnalités spécifiques -->
        <div class="test-section">
            <h3>🎯 Tests Spécifiques</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                <div>
                    <h4>📤 Export</h4>
                    <button class="btn-success" onclick="testExport()">Tester Export CSV</button>
                    <div id="exportResult" class="test-result"></div>
                </div>
                <div>
                    <h4>📥 Import</h4>
                    <button class="btn-warning" onclick="generateAndTestImport()">Générer & Tester Import</button>
                    <div id="importResult" class="test-result"></div>
                </div>
                <div>
                    <h4>🔍 Recherche</h4>
                    <button class="btn-primary" onclick="testSearch()">Tester Recherche</button>
                    <div id="searchResult" class="test-result"></div>
                </div>
                <div>
                    <h4>📋 Onglets</h4>
                    <button class="btn-primary" onclick="testTabs()">Tester Navigation</button>
                    <div id="tabsResult" class="test-result"></div>
                </div>
            </div>
        </div>

        <!-- Liens vers interfaces -->
        <div class="test-section">
            <h3>🔗 Accès Interfaces</h3>
            <div style="text-align: center;">
                <button class="btn-primary" onclick="window.open('/', '_blank')" style="margin: 10px; padding: 15px 30px; font-size: 16px;">
                    🖥️ Interface Principale
                </button>
                <button class="btn-success" onclick="window.open('/test-complet.html', '_blank')" style="margin: 10px; padding: 15px 30px; font-size: 16px;">
                    🧪 Tests Complets
                </button>
            </div>
        </div>
    </div>

    <script>
        let testResults = [];
        let currentTest = 0;
        let totalTests = 0;

        // Charger les statistiques
        async function loadStats() {
            try {
                const response = await fetch('/api/clients/stats');
                const stats = await response.json();
                
                document.getElementById('totalClients').textContent = stats.total;
                document.getElementById('thisMonth').textContent = stats.thisMonth;
                document.getElementById('thisYear').textContent = stats.thisYear;
                document.getElementById('averageAge').textContent = stats.averageAge || '-';
                
                addResult('statsResult', 'success', `✅ Statistiques chargées: ${stats.total} clients total, ${stats.thisMonth} ce mois`);
                return stats;
            } catch (error) {
                addResult('statsResult', 'error', `❌ Erreur statistiques: ${error.message}`);
                return null;
            }
        }

        // Lancer tous les tests
        async function runAllTests() {
            const btn = document.getElementById('runTestsBtn');
            btn.disabled = true;
            btn.textContent = '🔄 Tests en cours...';
            
            clearResults();
            currentTest = 0;
            totalTests = 8;
            
            const tests = [
                { name: 'API Clients', func: testClientsAPI },
                { name: 'API Statistiques', func: testStatsAPI },
                { name: 'API Références', func: testReferencesAPI },
                { name: 'Création Client', func: testCreateClient },
                { name: 'Modification Client', func: testUpdateClient },
                { name: 'Export CSV', func: testExport },
                { name: 'Recherche', func: testSearch },
                { name: 'Navigation Onglets', func: testTabs }
            ];

            for (const test of tests) {
                addResult('testResults', 'info', `🔄 Test en cours: ${test.name}...`);
                try {
                    await test.func();
                    addResult('testResults', 'success', `✅ ${test.name}: SUCCÈS`);
                } catch (error) {
                    addResult('testResults', 'error', `❌ ${test.name}: ÉCHEC - ${error.message}`);
                }
                currentTest++;
                updateProgress();
                await sleep(500); // Pause entre les tests
            }

            btn.disabled = false;
            btn.textContent = '🚀 Lancer Tous les Tests';
            addResult('testResults', 'success', `🎉 Tests terminés: ${currentTest}/${totalTests}`);
        }

        // Tests rapides
        async function runQuickTests() {
            clearResults();
            addResult('testResults', 'info', '⚡ Tests rapides en cours...');
            
            try {
                await testClientsAPI();
                await testStatsAPI();
                await testReferencesAPI();
                addResult('testResults', 'success', '✅ Tests rapides: TOUS RÉUSSIS');
            } catch (error) {
                addResult('testResults', 'error', `❌ Tests rapides: ÉCHEC - ${error.message}`);
            }
        }

        // Test CRUD complet
        async function testCRUD() {
            clearResults();
            addResult('testResults', 'info', '✏️ Test CRUD en cours...');
            
            try {
                const clientId = await testCreateClient();
                await testUpdateClient(clientId);
                await testDeleteClient(clientId);
                addResult('testResults', 'success', '✅ Test CRUD: COMPLET');
            } catch (error) {
                addResult('testResults', 'error', `❌ Test CRUD: ÉCHEC - ${error.message}`);
            }
        }

        // Tests individuels
        async function testClientsAPI() {
            const response = await fetch('/api/clients');
            if (!response.ok) throw new Error(`HTTP ${response.status}`);
            const clients = await response.json();
            if (!Array.isArray(clients)) throw new Error('Réponse invalide');
            return clients;
        }

        async function testStatsAPI() {
            const response = await fetch('/api/clients/stats');
            if (!response.ok) throw new Error(`HTTP ${response.status}`);
            const stats = await response.json();
            if (typeof stats.total !== 'number') throw new Error('Stats invalides');
            return stats;
        }

        async function testReferencesAPI() {
            const response = await fetch('/api/references/all');
            if (!response.ok) throw new Error(`HTTP ${response.status}`);
            const refs = await response.json();
            if (!refs.nationalities) throw new Error('Références incomplètes');
            return refs;
        }

        async function testCreateClient() {
            const clientData = {
                nom: 'TEST_AUTO',
                prenom: 'Client',
                email: `test.auto.${Date.now()}@example.com`,
                telephone: '0123456789'
            };

            const response = await fetch('/api/clients', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(clientData)
            });

            if (!response.ok) {
                const error = await response.text();
                throw new Error(`Création échouée: ${error}`);
            }

            const result = await response.json();
            return result.id;
        }

        async function testUpdateClient(clientId) {
            if (!clientId) {
                // Récupérer le dernier client
                const clients = await testClientsAPI();
                clientId = clients[clients.length - 1]?.id;
            }

            if (!clientId) throw new Error('Aucun client à modifier');

            const updateData = {
                nom: 'TEST_MODIFIE',
                prenom: 'Client_Modifie',
                email: `test.modifie.${Date.now()}@example.com`,
                ville: 'Ville Modifiée'
            };

            const response = await fetch(`/api/clients/${clientId}`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(updateData)
            });

            if (!response.ok) {
                const error = await response.text();
                throw new Error(`Modification échouée: ${error}`);
            }

            return clientId;
        }

        async function testDeleteClient(clientId) {
            if (!clientId) return;

            const response = await fetch(`/api/clients/${clientId}`, {
                method: 'DELETE'
            });

            if (!response.ok) {
                const error = await response.text();
                throw new Error(`Suppression échouée: ${error}`);
            }
        }

        async function testExport() {
            try {
                const clients = await testClientsAPI();
                addResult('exportResult', 'success', `✅ Export simulé: ${clients.length} clients prêts`);
            } catch (error) {
                addResult('exportResult', 'error', `❌ Export: ${error.message}`);
            }
        }

        async function generateAndTestImport() {
            addResult('importResult', 'info', '📥 Génération fichier test...');
            
            // Simuler un import
            const testData = [
                { nom: 'IMPORT1', prenom: 'Test', email: '<EMAIL>' },
                { nom: 'IMPORT2', prenom: 'Test', email: '<EMAIL>' }
            ];
            
            addResult('importResult', 'success', `✅ Import simulé: ${testData.length} clients prêts`);
        }

        async function testSearch() {
            addResult('searchResult', 'info', '🔍 Test recherche...');
            // Simuler une recherche
            await sleep(500);
            addResult('searchResult', 'success', '✅ Recherche: Fonctionnelle');
        }

        async function testTabs() {
            addResult('tabsResult', 'info', '📋 Test navigation onglets...');
            // Simuler test onglets
            await sleep(500);
            addResult('tabsResult', 'success', '✅ Onglets: Navigation OK');
        }

        // Utilitaires
        function addResult(containerId, type, message) {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
            container.appendChild(div);
            container.scrollTop = container.scrollHeight;
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
            updateProgress(0);
        }

        function updateProgress(percent = null) {
            if (percent === null) {
                percent = totalTests > 0 ? (currentTest / totalTests) * 100 : 0;
            }
            document.getElementById('progressBar').style.width = `${percent}%`;
        }

        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // Charger les stats au démarrage
        document.addEventListener('DOMContentLoaded', () => {
            loadStats();
        });
    </script>
</body>
</html>
