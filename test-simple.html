<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Simple Onglets</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .tabs-nav { display: flex; gap: 10px; margin-bottom: 20px; }
        .tab-btn { 
            padding: 10px 20px; 
            border: 1px solid #ccc; 
            background: #f0f0f0; 
            cursor: pointer; 
            border-radius: 5px;
        }
        .tab-btn.active { background: #007bff; color: white; }
        .tab-content { 
            display: none; 
            padding: 20px; 
            border: 1px solid #ccc; 
            border-radius: 5px;
        }
        .tab-content.active { display: block; }
        .form-group { margin: 10px 0; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input, .form-group select { 
            width: 100%; 
            padding: 8px; 
            border: 1px solid #ccc; 
            border-radius: 3px;
        }
        .btn { 
            padding: 10px 20px; 
            margin: 5px; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
    </style>
</head>
<body>
    <h1>🧪 Test Simple des Onglets</h1>
    
    <div class="tabs-nav">
        <button class="tab-btn active" data-tab="general">Général</button>
        <button class="tab-btn" data-tab="identity">Identité</button>
        <button class="tab-btn" data-tab="contact">Contact</button>
        <button class="tab-btn" data-tab="travel">Voyage</button>
    </div>
    
    <form id="testForm">
        <div class="tab-content active" id="tab-general">
            <h3>Informations Générales</h3>
            <div class="form-group">
                <label for="nom">Nom</label>
                <input type="text" id="nom" name="nom" placeholder="Nom">
            </div>
            <div class="form-group">
                <label for="prenom">Prénom</label>
                <input type="text" id="prenom" name="prenom" placeholder="Prénom">
            </div>
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" placeholder="Email">
            </div>
        </div>
        
        <div class="tab-content" id="tab-identity">
            <h3>Documents d'Identité</h3>
            <div class="form-group">
                <label for="cin">N° CIN</label>
                <input type="text" id="cin" name="cin" placeholder="N° CIN">
            </div>
            <div class="form-group">
                <label for="passeport">N° Passeport</label>
                <input type="text" id="passeport" name="passeport" placeholder="N° Passeport">
            </div>
        </div>
        
        <div class="tab-content" id="tab-contact">
            <h3>Informations de Contact</h3>
            <div class="form-group">
                <label for="telephone">Téléphone</label>
                <input type="tel" id="telephone" name="telephone" placeholder="Téléphone">
            </div>
            <div class="form-group">
                <label for="adresse">Adresse</label>
                <input type="text" id="adresse" name="adresse" placeholder="Adresse">
            </div>
        </div>
        
        <div class="tab-content" id="tab-travel">
            <h3>Informations Voyage</h3>
            <div class="form-group">
                <label for="compagnie">Compagnie Préférée</label>
                <select id="compagnie" name="compagnie">
                    <option value="">Sélectionner...</option>
                    <option value="ram">Royal Air Maroc</option>
                    <option value="air-france">Air France</option>
                </select>
            </div>
            <div class="form-group">
                <label for="carte-fidelite">N° Carte Fidélité</label>
                <input type="text" id="carte-fidelite" name="carte-fidelite" placeholder="N° Carte">
            </div>
        </div>
        
        <div style="margin-top: 20px;">
            <button type="submit" class="btn btn-primary">Enregistrer</button>
            <button type="button" class="btn btn-secondary" onclick="resetForm()">Réinitialiser</button>
        </div>
    </form>

    <script>
        console.log('🚀 Initialisation du test...');
        
        // Fonction pour changer d'onglet
        function switchTab(tabName) {
            console.log('🔄 Changement vers onglet:', tabName);
            
            // Mettre à jour les boutons
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.tab === tabName);
            });
            
            // Mettre à jour le contenu
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.toggle('active', content.id === `tab-${tabName}`);
            });
        }
        
        // Ajouter les event listeners
        document.addEventListener('DOMContentLoaded', () => {
            console.log('📋 DOM chargé');
            
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('🖱️ Clic sur:', btn.dataset.tab);
                    switchTab(btn.dataset.tab);
                });
            });
            
            document.getElementById('testForm').addEventListener('submit', (e) => {
                e.preventDefault();
                console.log('📝 Soumission du formulaire');
                const formData = new FormData(e.target);
                const data = Object.fromEntries(formData.entries());
                console.log('📋 Données:', data);
                alert('Formulaire soumis ! Voir la console pour les données.');
            });
        });
        
        function resetForm() {
            document.getElementById('testForm').reset();
            switchTab('general');
        }
    </script>
</body>
</html>
