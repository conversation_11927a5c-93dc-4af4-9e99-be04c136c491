const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs-extra');
const router = express.Router();
const clientsDB = require('../database');

// Créer les dossiers s'ils n'existent pas
const uploadsDir = path.join(__dirname, '../uploads');
const photosDir = path.join(uploadsDir, 'photos');
const documentsDir = path.join(uploadsDir, 'documents');

fs.ensureDirSync(photosDir);
fs.ensureDirSync(documentsDir);

// Configuration de multer pour les photos
const photoStorage = multer.diskStorage({
    destination: (req, file, cb) => {
        cb(null, photosDir);
    },
    filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, 'photo-' + uniqueSuffix + path.extname(file.originalname));
    }
});

// Configuration de multer pour les documents
const documentStorage = multer.diskStorage({
    destination: (req, file, cb) => {
        cb(null, documentsDir);
    },
    filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, 'doc-' + uniqueSuffix + path.extname(file.originalname));
    }
});

// Filtres de fichiers
const photoFilter = (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
        cb(null, true);
    } else {
        cb(new Error('Seules les images sont autorisées pour les photos'), false);
    }
};

const documentFilter = (req, file, cb) => {
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'];
    if (allowedTypes.includes(file.mimetype)) {
        cb(null, true);
    } else {
        cb(new Error('Seuls les fichiers PDF et images sont autorisés pour les documents'), false);
    }
};

// Middleware multer
const uploadPhoto = multer({
    storage: photoStorage,
    fileFilter: photoFilter,
    limits: { fileSize: 5 * 1024 * 1024 } // 5MB max
});

const uploadDocument = multer({
    storage: documentStorage,
    fileFilter: documentFilter,
    limits: { fileSize: 10 * 1024 * 1024 } // 10MB max
});

// POST /api/upload/photo/:clientId - Upload photo de profil
router.post('/photo/:clientId', uploadPhoto.single('photo'), (req, res) => {
    if (!req.file) {
        return res.status(400).json({ error: 'Aucun fichier photo fourni' });
    }

    const clientId = req.params.clientId;
    const photoUrl = `/uploads/photos/${req.file.filename}`;

    // Mettre à jour le client avec l'URL de la photo
    clientsDB.updateClient(clientId, { photo_url: photoUrl }, (err) => {
        if (err) {
            // Supprimer le fichier en cas d'erreur
            fs.unlink(req.file.path, () => {});
            return res.status(500).json({ error: 'Erreur lors de la mise à jour de la photo' });
        }

        res.json({
            message: 'Photo uploadée avec succès',
            photoUrl: photoUrl,
            filename: req.file.filename
        });
    });
});

// POST /api/upload/document/:clientId - Upload document
router.post('/document/:clientId', uploadDocument.single('document'), (req, res) => {
    if (!req.file) {
        return res.status(400).json({ error: 'Aucun fichier document fourni' });
    }

    const clientId = req.params.clientId;
    const typeDocument = req.body.type_document || 'autre';

    const documentData = {
        client_id: clientId,
        type_document: typeDocument,
        nom_fichier: req.file.originalname,
        chemin_fichier: `/uploads/documents/${req.file.filename}`,
        taille_fichier: req.file.size
    };

    clientsDB.addDocument(documentData, (err, documentId) => {
        if (err) {
            // Supprimer le fichier en cas d'erreur
            fs.unlink(req.file.path, () => {});
            return res.status(500).json({ error: 'Erreur lors de l\'enregistrement du document' });
        }

        res.json({
            message: 'Document uploadé avec succès',
            documentId: documentId,
            filename: req.file.filename,
            originalName: req.file.originalname
        });
    });
});

// GET /api/upload/documents/:clientId - Obtenir les documents d'un client
router.get('/documents/:clientId', (req, res) => {
    const clientId = req.params.clientId;

    clientsDB.getClientDocuments(clientId, (err, documents) => {
        if (err) {
            return res.status(500).json({ error: 'Erreur lors de la récupération des documents' });
        }
        res.json(documents);
    });
});

// DELETE /api/upload/document/:documentId - Supprimer un document
router.delete('/document/:documentId', (req, res) => {
    const documentId = req.params.documentId;

    clientsDB.deleteDocument(documentId, (err, document) => {
        if (err) {
            return res.status(500).json({ error: 'Erreur lors de la suppression du document' });
        }

        // Supprimer le fichier physique
        const filePath = path.join(__dirname, '..', document.chemin_fichier);
        fs.unlink(filePath, (unlinkErr) => {
            if (unlinkErr) {
                console.error('Erreur lors de la suppression du fichier:', unlinkErr);
            }
        });

        res.json({ message: 'Document supprimé avec succès' });
    });
});

// GET /api/upload/document/:documentId/download - Télécharger un document
router.get('/document/:documentId/download', (req, res) => {
    const documentId = req.params.documentId;

    clientsDB.getDocumentById(documentId, (err, document) => {
        if (err) {
            return res.status(500).json({ error: 'Erreur lors de la récupération du document' });
        }
        if (!document) {
            return res.status(404).json({ error: 'Document non trouvé' });
        }

        const filePath = path.join(__dirname, '..', document.chemin_fichier);
        
        // Vérifier si le fichier existe
        if (!fs.existsSync(filePath)) {
            return res.status(404).json({ error: 'Fichier non trouvé sur le serveur' });
        }

        res.download(filePath, document.nom_fichier);
    });
});

module.exports = router;
