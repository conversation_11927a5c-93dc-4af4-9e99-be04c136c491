const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Connexion à la base de données
const dbPath = path.join(__dirname, 'backend', 'database.db');
const db = new sqlite3.Database(dbPath);

console.log('🔍 Vérification des emails existants dans la base de données...\n');

// Récupérer tous les emails existants
db.all("SELECT id, nom, prenom, email FROM clients ORDER BY id", (err, clients) => {
    if (err) {
        console.error('❌ Erreur:', err);
        return;
    }

    console.log(`📊 ${clients.length} clients trouvés dans la base :\n`);
    
    clients.forEach(client => {
        console.log(`${client.id}. ${client.nom} ${client.prenom} - ${client.email}`);
    });

    console.log('\n🎯 Emails du fichier de test qui vont causer des conflits :');
    const testEmails = [
        '<EMAIL>',
        '<EMAIL>', 
        '<EMAIL>',
        '<EMAIL>'
    ];

    const existingEmails = clients.map(c => c.email.toLowerCase());
    
    testEmails.forEach((email, index) => {
        if (existingEmails.includes(email.toLowerCase())) {
            console.log(`❌ Ligne ${index + 2}: ${email} - CONFLIT DÉTECTÉ`);
        } else {
            console.log(`✅ Ligne ${index + 2}: ${email} - OK`);
        }
    });

    console.log('\n💡 Solution : Générer des emails uniques avec timestamp');
    
    db.close();
});
