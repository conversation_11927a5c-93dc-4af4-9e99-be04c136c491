# 📋 Nouveaux Champs de la Table Clients

## 🎯 Vue d'ensemble

La table `clients` a été étendue avec de nombreux nouveaux champs pour une gestion complète des informations clients. Voici la liste détaillée de tous les nouveaux champs ajoutés :

## 🆔 Identification

### Code_Client
- **Type** : VARCHAR(20) UNIQUE
- **Description** : Code client généré automatiquement
- **Format** : CLI000001, CLI000002, etc.
- **Génération** : Automatique via trigger SQL

### CIN_No
- **Type** : VARCHAR(20)
- **Description** : Numéro de carte d'identité nationale
- **Exemple** : "AB123456"

### Passeport_No
- **Type** : VARCHAR(20)
- **Description** : Numéro de passeport
- **Exemple** : "M1234567"

### Date_Expiration_Passeport
- **Type** : DATE
- **Description** : Date d'expiration du passeport
- **Format** : YYYY-MM-DD

## 👤 Informations Personnelles

### Nationality
- **Type** : INTEGER (clé étrangère vers `nationality`)
- **Description** : Nationalité du client
- **Table de référence** : `nationality` (id, code, nom_fr, nom_en, nom_ar)

### Sexe
- **Type** : INTEGER (clé étrangère vers `sexe`)
- **Description** : Sexe du client
- **Table de référence** : `sexe` (id, code, libelle_fr, libelle_en, libelle_ar)
- **Valeurs** : M (Masculin), F (Féminin)

### Situation_Familiale
- **Type** : INTEGER (clé étrangère vers `situation_familiale`)
- **Description** : Situation familiale du client
- **Table de référence** : `situation_familiale` (id, libelle_fr, libelle_en, libelle_ar)
- **Valeurs** : Célibataire, Marié(e), Divorcé(e), Veuf/Veuve

### Date_de_Naissance
- **Type** : DATE
- **Description** : Date de naissance du client
- **Format** : YYYY-MM-DD
- **Note** : L'âge est calculé automatiquement à partir de cette date

### Pays_de_Naissance
- **Type** : INTEGER (clé étrangère vers `pays_de_naissance`)
- **Description** : Pays de naissance
- **Table de référence** : `pays_de_naissance` (id, code, nom_fr, nom_en, nom_ar)

### Lieu_de_Naissance
- **Type** : INTEGER (clé étrangère vers `lieu_de_naissance`)
- **Description** : Ville/lieu de naissance
- **Table de référence** : `lieu_de_naissance` (id, nom_fr, nom_en, nom_ar, pays_id)

## 📞 Contacts

### Label_Tel
- **Type** : INTEGER (clé étrangère vers `label_tel`)
- **Description** : Type de téléphone principal
- **Table de référence** : `label_tel` (id, libelle)
- **Valeurs** : Domicile, Bureau, Autre

### Mobile_1
- **Type** : VARCHAR(20)
- **Description** : Premier numéro de mobile
- **Exemple** : "+212661234567"

### Label_Mobile_1
- **Type** : INTEGER (clé étrangère vers `label_mobile`)
- **Description** : Type du premier mobile
- **Table de référence** : `label_mobile` (id, libelle)
- **Valeurs** : Personnel, Professionnel, Urgence, Autre

### Mobile_2
- **Type** : VARCHAR(20)
- **Description** : Deuxième numéro de mobile
- **Exemple** : "+212662345678"

### Label_Mobile_2
- **Type** : INTEGER (clé étrangère vers `label_mobile`)
- **Description** : Type du deuxième mobile

## 🌍 Informations Multilingues

### Nom_Arabe
- **Type** : NVARCHAR(100)
- **Description** : Nom du client en arabe
- **Exemple** : "الحبيبي"

### Prenom_Arabe
- **Type** : NVARCHAR(100)
- **Description** : Prénom du client en arabe
- **Exemple** : "كريم"

## ✈️ Fidélité Compagnies Aériennes

### Loyalty_Card_No
- **Type** : VARCHAR(30)
- **Description** : Numéro de carte de fidélité
- **Format** : Alphanumérique
- **Exemple** : "RAM123456789"

### Airline_Code_for_Loyalty_Card
- **Type** : INTEGER (clé étrangère vers `airline_code_for_loyalty_card`)
- **Description** : Compagnie aérienne de la carte de fidélité
- **Table de référence** : `airline_code_for_loyalty_card` (id, code, nom, pays)
- **Exemples** : RAM (Royal Air Maroc), AFR (Air France), IBE (Iberia)

## 👨‍👩‍👧‍👦 Contact d'Urgence

### Related_Name
- **Type** : VARCHAR(100)
- **Description** : Nom de la personne à contacter
- **Exemple** : "Fatima Habibi"

### Label_Related_Name
- **Type** : INTEGER (clé étrangère vers `label_related_name`)
- **Description** : Relation avec la personne
- **Table de référence** : `label_related_name` (id, libelle)
- **Valeurs** : Époux/Épouse, Père, Mère, Fils, Fille, Frère, Sœur, Contact d'urgence

### Tel_Related_Name
- **Type** : VARCHAR(20)
- **Description** : Téléphone de la personne à contacter
- **Exemple** : "+212661987654"

## 💰 Comptabilité

### Compte_Comptable
- **Type** : INTEGER (clé étrangère vers `compte_comptable`)
- **Description** : Compte comptable associé au client
- **Table de référence** : `compte_comptable` (id, numero_compte, libelle, type_compte)
- **Exemples** : 411001 (Clients particuliers), 411002 (Clients entreprises)

## 🔧 API Endpoints

### Données de Référence
- `GET /api/references/all` - Toutes les données de référence
- `GET /api/references/nationalities` - Nationalités
- `GET /api/references/sexes` - Sexes
- `GET /api/references/situations-familiales` - Situations familiales
- `GET /api/references/pays-naissance` - Pays de naissance
- `GET /api/references/lieux-naissance` - Lieux de naissance
- `GET /api/references/labels-tel` - Labels téléphone
- `GET /api/references/labels-mobile` - Labels mobile
- `GET /api/references/airline-codes` - Codes compagnies aériennes
- `GET /api/references/labels-related-name` - Labels nom apparenté
- `GET /api/references/comptes-comptables` - Comptes comptables

### Clients avec Relations
- `GET /api/clients/:id` - Client avec toutes ses relations (noms des références au lieu des IDs)

## 📊 Calculs Automatiques

### Âge
L'âge n'est pas stocké en base mais calculé automatiquement à partir de `date_de_naissance` :

```javascript
function calculateAge(birthDate) {
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
        age--;
    }
    
    return age;
}
```

## 🔄 Migration

La migration a été effectuée automatiquement avec :
- ✅ Création de toutes les tables de référence
- ✅ Ajout de toutes les nouvelles colonnes
- ✅ Génération automatique des codes clients existants
- ✅ Insertion des données de référence par défaut

## 📝 Notes Importantes

1. **Clés étrangères** : Gérées au niveau applicatif (SQLite ne supporte pas ADD CONSTRAINT)
2. **Code client** : Généré automatiquement via trigger SQL
3. **Données multilingues** : Support français, anglais et arabe
4. **Compatibilité** : Tous les anciens clients continuent de fonctionner
5. **Performance** : Index créés sur les champs de recherche fréquents
