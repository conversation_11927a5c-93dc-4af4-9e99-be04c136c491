// Variables globales
let clients = [];
let currentClientId = null;
let isEditing = false;
let currentPhotoFile = null;
let clientDocuments = [];
let existingPhotoUrl = null;

// Éléments DOM
const clientsContainer = document.getElementById('clientsContainer');
const clientModal = document.getElementById('clientModal');
const deleteModal = document.getElementById('deleteModal');
const clientForm = document.getElementById('clientForm');
const modalTitle = document.getElementById('modalTitle');
const searchInput = document.getElementById('searchInput');

// Boutons
const addClientBtn = document.getElementById('addClientBtn');
const searchBtn = document.getElementById('searchBtn');
const clearSearchBtn = document.getElementById('clearSearchBtn');
const cancelBtn = document.getElementById('cancelBtn');
const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');

// Éléments pour photos et documents
const photoInput = document.getElementById('photoInput');
const selectPhotoBtn = document.getElementById('selectPhotoBtn');
const removePhotoBtn = document.getElementById('removePhotoBtn');
const photoPreview = document.getElementById('photoPreview');
const previewImage = document.getElementById('previewImage');
const documentsSection = document.getElementById('documentsSection');
const documentInput = document.getElementById('documentInput');
const uploadDocumentBtn = document.getElementById('uploadDocumentBtn');
const documentType = document.getElementById('documentType');
const documentsContainer = document.getElementById('documentsContainer');

// Event listeners
document.addEventListener('DOMContentLoaded', loadClients);
addClientBtn.addEventListener('click', openAddModal);
searchBtn.addEventListener('click', searchClients);
clearSearchBtn.addEventListener('click', clearSearch);
clientForm.addEventListener('submit', handleFormSubmit);
cancelBtn.addEventListener('click', closeModal);
confirmDeleteBtn.addEventListener('click', confirmDelete);
cancelDeleteBtn.addEventListener('click', closeDeleteModal);

// Event listeners pour photos et documents
selectPhotoBtn.addEventListener('click', () => photoInput.click());
removePhotoBtn.addEventListener('click', removePhoto);
photoInput.addEventListener('change', handlePhotoSelect);
uploadDocumentBtn.addEventListener('click', uploadDocument);

// Fermer les modals en cliquant sur X ou en dehors
document.querySelectorAll('.close').forEach(closeBtn => {
    closeBtn.addEventListener('click', closeModal);
});

window.addEventListener('click', (e) => {
    if (e.target === clientModal) closeModal();
    if (e.target === deleteModal) closeDeleteModal();
});

// Recherche en temps réel
searchInput.addEventListener('input', debounce(searchClients, 300));

// Fonction de debounce pour la recherche
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Charger tous les clients
async function loadClients() {
    try {
        showLoading();
        const response = await fetch('/api/clients');
        if (!response.ok) throw new Error('Erreur lors du chargement');
        
        clients = await response.json();
        displayClients(clients);
    } catch (error) {
        showError('Erreur lors du chargement des clients: ' + error.message);
    }
}

// Rechercher des clients
async function searchClients() {
    const searchTerm = searchInput.value.trim();
    
    if (!searchTerm) {
        loadClients();
        return;
    }

    try {
        showLoading();
        const response = await fetch(`/api/clients/search?q=${encodeURIComponent(searchTerm)}`);
        if (!response.ok) throw new Error('Erreur lors de la recherche');
        
        const searchResults = await response.json();
        displayClients(searchResults);
    } catch (error) {
        showError('Erreur lors de la recherche: ' + error.message);
    }
}

// Effacer la recherche
function clearSearch() {
    searchInput.value = '';
    loadClients();
}

// Afficher les clients
function displayClients(clientsList) {
    if (clientsList.length === 0) {
        clientsContainer.innerHTML = `
            <div class="no-clients">
                <h3>Aucun client trouvé</h3>
                <p>Commencez par ajouter votre premier client !</p>
            </div>
        `;
        return;
    }

    clientsContainer.innerHTML = clientsList.map(client => `
        <div class="client-card">
            <div class="client-photo">
                ${client.photo_url ?
                    `<img src="${client.photo_url}" alt="Photo de ${client.nom} ${client.prenom}">` :
                    '<div class="no-photo">👤</div>'
                }
            </div>
            <div class="client-content">
                <div class="client-header">
                    <div class="client-name">${client.nom} ${client.prenom}</div>
                    <div class="client-actions">
                        <button class="btn-edit" onclick="editClient(${client.id})">✏️ Modifier</button>
                        <button class="btn-delete" onclick="deleteClient(${client.id})">🗑️ Supprimer</button>
                    </div>
                </div>
            <div class="client-info">
                <div class="info-item">
                    <span class="info-label">📧 Email:</span>
                    <span>${client.email}</span>
                </div>
                ${client.telephone ? `
                    <div class="info-item">
                        <span class="info-label">📞 Tél:</span>
                        <span>${client.telephone}</span>
                    </div>
                ` : ''}
                ${client.adresse ? `
                    <div class="info-item">
                        <span class="info-label">🏠 Adresse:</span>
                        <span>${client.adresse}</span>
                    </div>
                ` : ''}
                ${client.ville ? `
                    <div class="info-item">
                        <span class="info-label">🏙️ Ville:</span>
                        <span>${client.ville} ${client.code_postal || ''}</span>
                    </div>
                ` : ''}
                <div class="info-item">
                    <span class="info-label">📅 Créé:</span>
                    <span>${formatDate(client.date_creation)}</span>
                </div>
            </div>
            </div>
        </div>
    `).join('');
}

// Ouvrir le modal d'ajout
function openAddModal() {
    isEditing = false;
    currentClientId = null;
    existingPhotoUrl = null;
    modalTitle.textContent = 'Ajouter un Client';
    clientForm.reset();
    resetPhotoPreview();
    documentsSection.style.display = 'none';
    clientModal.style.display = 'block';
}

// Modifier un client
async function editClient(id) {
    try {
        const response = await fetch(`/api/clients/${id}`);
        if (!response.ok) throw new Error('Client non trouvé');
        
        const client = await response.json();
        
        isEditing = true;
        currentClientId = id;
        modalTitle.textContent = 'Modifier le Client';
        
        // Remplir le formulaire
        document.getElementById('nom').value = client.nom;
        document.getElementById('prenom').value = client.prenom;
        document.getElementById('email').value = client.email;
        document.getElementById('telephone').value = client.telephone || '';
        document.getElementById('adresse').value = client.adresse || '';
        document.getElementById('ville').value = client.ville || '';
        document.getElementById('code_postal').value = client.code_postal || '';

        // Afficher la photo existante
        existingPhotoUrl = client.photo_url;
        if (client.photo_url) {
            previewImage.src = client.photo_url;
            previewImage.style.display = 'block';
            document.querySelector('.photo-placeholder').style.display = 'none';
            removePhotoBtn.style.display = 'inline-block';
        } else {
            resetPhotoPreview();
        }

        // Afficher la section documents et charger les documents
        documentsSection.style.display = 'block';
        loadClientDocuments(id);

        clientModal.style.display = 'block';
    } catch (error) {
        showError('Erreur lors du chargement du client: ' + error.message);
    }
}

// Supprimer un client
function deleteClient(id) {
    currentClientId = id;
    deleteModal.style.display = 'block';
}

// Confirmer la suppression
async function confirmDelete() {
    try {
        const response = await fetch(`/api/clients/${currentClientId}`, {
            method: 'DELETE'
        });
        
        if (!response.ok) throw new Error('Erreur lors de la suppression');
        
        showSuccess('Client supprimé avec succès');
        closeDeleteModal();
        loadClients();
    } catch (error) {
        showError('Erreur lors de la suppression: ' + error.message);
    }
}

// Gérer la soumission du formulaire
async function handleFormSubmit(e) {
    e.preventDefault();

    const formData = new FormData(clientForm);
    const clientData = Object.fromEntries(formData.entries());

    // Si on est en mode édition et qu'il n'y a pas de nouvelle photo,
    // on garde l'URL de la photo existante
    if (isEditing && !currentPhotoFile && existingPhotoUrl) {
        clientData.photo_url = existingPhotoUrl;
    }

    try {
        // Étape 1: Sauvegarder/mettre à jour le client
        const url = isEditing ? `/api/clients/${currentClientId}` : '/api/clients';
        const method = isEditing ? 'PUT' : 'POST';

        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(clientData)
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || 'Erreur lors de l\'enregistrement');
        }

        const result = await response.json();
        const clientId = isEditing ? currentClientId : result.id;

        // Étape 2: Upload de la photo si une nouvelle photo a été sélectionnée
        if (currentPhotoFile) {
            await uploadPhoto(clientId);
        }

        const message = isEditing ? 'Client modifié avec succès' : 'Client ajouté avec succès';
        showSuccess(message);
        closeModal();
        loadClients();
    } catch (error) {
        showError(error.message);
    }
}

// Fonction pour uploader une photo
async function uploadPhoto(clientId) {
    const formData = new FormData();
    formData.append('photo', currentPhotoFile);

    const response = await fetch(`/api/upload/photo/${clientId}`, {
        method: 'POST',
        body: formData
    });

    if (!response.ok) {
        const error = await response.json();
        throw new Error('Erreur lors de l\'upload de la photo: ' + error.error);
    }

    currentPhotoFile = null;
}

// Fermer le modal principal
function closeModal() {
    clientModal.style.display = 'none';
    clientForm.reset();
    resetPhotoPreview();
    currentClientId = null;
    isEditing = false;
    currentPhotoFile = null;
    existingPhotoUrl = null;
    documentsSection.style.display = 'none';
}

// Fermer le modal de suppression
function closeDeleteModal() {
    deleteModal.style.display = 'none';
    currentClientId = null;
}

// Afficher le chargement
function showLoading() {
    clientsContainer.innerHTML = '<div class="loading">Chargement des clients...</div>';
}

// Afficher une erreur
function showError(message) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.textContent = message;
    
    document.querySelector('.container').insertBefore(errorDiv, document.querySelector('.clients-section'));
    
    setTimeout(() => errorDiv.remove(), 5000);
}

// Afficher un succès
function showSuccess(message) {
    const successDiv = document.createElement('div');
    successDiv.className = 'success-message';
    successDiv.textContent = message;
    
    document.querySelector('.container').insertBefore(successDiv, document.querySelector('.clients-section'));
    
    setTimeout(() => successDiv.remove(), 3000);
}

// Formater une date
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

// Fonctions pour gérer les photos
function handlePhotoSelect(e) {
    const file = e.target.files[0];
    if (file) {
        currentPhotoFile = file;
        const reader = new FileReader();
        reader.onload = (e) => {
            previewImage.src = e.target.result;
            previewImage.style.display = 'block';
            document.querySelector('.photo-placeholder').style.display = 'none';
            removePhotoBtn.style.display = 'inline-block';
        };
        reader.readAsDataURL(file);
    }
}

function removePhoto() {
    currentPhotoFile = null;
    existingPhotoUrl = null; // Supprimer aussi la photo existante
    resetPhotoPreview();
}

function resetPhotoPreview() {
    previewImage.src = '';
    previewImage.style.display = 'none';
    document.querySelector('.photo-placeholder').style.display = 'block';
    removePhotoBtn.style.display = 'none';
    photoInput.value = '';
}

// Fonctions pour gérer les documents
async function loadClientDocuments(clientId) {
    try {
        const response = await fetch(`/api/upload/documents/${clientId}`);
        if (!response.ok) throw new Error('Erreur lors du chargement des documents');

        clientDocuments = await response.json();
        displayDocuments();
    } catch (error) {
        console.error('Erreur:', error);
        documentsContainer.innerHTML = '<div class="error">Erreur lors du chargement des documents</div>';
    }
}

function displayDocuments() {
    if (clientDocuments.length === 0) {
        documentsContainer.innerHTML = '<div class="no-documents">Aucun document ajouté</div>';
        return;
    }

    documentsContainer.innerHTML = clientDocuments.map(doc => `
        <div class="document-item">
            <div class="document-info">
                <span class="document-type">${getDocumentTypeLabel(doc.type_document)}</span>
                <span class="document-name">${doc.nom_fichier}</span>
                <span class="document-size">(${formatFileSize(doc.taille_fichier)})</span>
            </div>
            <div class="document-actions">
                <button class="btn-download" onclick="downloadDocument(${doc.id})">⬇️ Télécharger</button>
                <button class="btn-delete" onclick="deleteDocument(${doc.id})">🗑️ Supprimer</button>
            </div>
        </div>
    `).join('');
}

async function uploadDocument() {
    const file = documentInput.files[0];
    if (!file) {
        showError('Veuillez sélectionner un fichier');
        return;
    }

    if (!currentClientId) {
        showError('Erreur: ID client non trouvé');
        return;
    }

    const formData = new FormData();
    formData.append('document', file);
    formData.append('type_document', documentType.value);

    try {
        const response = await fetch(`/api/upload/document/${currentClientId}`, {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || 'Erreur lors de l\'upload');
        }

        showSuccess('Document ajouté avec succès');
        documentInput.value = '';
        loadClientDocuments(currentClientId);
    } catch (error) {
        showError('Erreur lors de l\'upload: ' + error.message);
    }
}

async function deleteDocument(documentId) {
    if (!confirm('Êtes-vous sûr de vouloir supprimer ce document ?')) return;

    try {
        const response = await fetch(`/api/upload/document/${documentId}`, {
            method: 'DELETE'
        });

        if (!response.ok) throw new Error('Erreur lors de la suppression');

        showSuccess('Document supprimé avec succès');
        loadClientDocuments(currentClientId);
    } catch (error) {
        showError('Erreur lors de la suppression: ' + error.message);
    }
}

function downloadDocument(documentId) {
    window.open(`/api/upload/document/${documentId}/download`, '_blank');
}

function getDocumentTypeLabel(type) {
    const types = {
        'passeport': '🛂 Passeport',
        'carte_identite': '🆔 Carte d\'identité',
        'permis_conduire': '🚗 Permis de conduire',
        'autre': '📄 Autre'
    };
    return types[type] || '📄 Document';
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
