// Variables globales
let clients = [];
let currentClientId = null;
let isEditing = false;
let currentPhotoFile = null;
let clientDocuments = [];
let existingPhotoUrl = null;
let referencesData = {};
let currentTab = 'general';

// Variables pour le déplacement du modal
let isDragging = false;
let dragOffset = { x: 0, y: 0 };

// Éléments DOM
const clientsContainer = document.getElementById('clientsContainer');
const clientModal = document.getElementById('clientModal');
const deleteModal = document.getElementById('deleteModal');
const clientForm = document.getElementById('clientForm');
const modalTitle = document.getElementById('modalTitle');
const searchInput = document.getElementById('searchInput');

// Boutons
const addClientBtn = document.getElementById('addClientBtn');
const searchBtn = document.getElementById('searchBtn');
const clearSearchBtn = document.getElementById('clearSearchBtn');
const cancelBtn = document.getElementById('cancelBtn');
const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');

// Éléments pour photos et documents
const photoInput = document.getElementById('photoInput');
const selectPhotoBtn = document.getElementById('selectPhotoBtn');
const removePhotoBtn = document.getElementById('removePhotoBtn');
const photoPreview = document.getElementById('photoPreview');
const previewImage = document.getElementById('previewImage');
const documentsSection = document.getElementById('documentsSection');
const documentInput = document.getElementById('documentInput');
const uploadDocumentBtn = document.getElementById('uploadDocumentBtn');
const documentType = document.getElementById('documentType');
const documentsContainer = document.getElementById('documentsContainer');

// Éléments pour les onglets - seront initialisés dans DOMContentLoaded
let tabButtons = [];
let tabContents = [];

// Éléments pour les statistiques
const totalClientsEl = document.getElementById('totalClients');
const newClientsMonthEl = document.getElementById('newClientsMonth');

// Éléments pour les filtres
const filterNationality = document.getElementById('filterNationality');
const filterSexe = document.getElementById('filterSexe');

// Event listeners - seront initialisés dans DOMContentLoaded

// Event listeners supprimés - maintenant dans DOMContentLoaded

// Initialisation complète de l'application
document.addEventListener('DOMContentLoaded', async () => {
    console.log('🚀 Initialisation de l\'application...');

    // Vérifier que les éléments DOM critiques existent
    console.log('🔍 Vérification des éléments DOM...');
    console.log('clientsContainer:', clientsContainer);
    console.log('addClientBtn:', addClientBtn);
    console.log('clientModal:', clientModal);
    console.log('clientForm:', clientForm);
    console.log('cancelBtn:', cancelBtn);

    if (!clientsContainer) {
        console.error('❌ ERREUR CRITIQUE: clientsContainer non trouvé !');
        return;
    }

    // Initialiser tous les event listeners
    console.log('🔧 Initialisation des event listeners...');

    // Event listeners principaux
    if (addClientBtn) addClientBtn.addEventListener('click', openAddModal);
    if (searchBtn) searchBtn.addEventListener('click', searchClients);
    if (clearSearchBtn) clearSearchBtn.addEventListener('click', clearSearch);
    if (clientForm) clientForm.addEventListener('submit', handleFormSubmit);
    if (cancelBtn) cancelBtn.addEventListener('click', closeModal);
    if (confirmDeleteBtn) confirmDeleteBtn.addEventListener('click', confirmDelete);
    if (cancelDeleteBtn) cancelDeleteBtn.addEventListener('click', closeDeleteModal);

    // Event listeners pour photos et documents
    if (selectPhotoBtn) selectPhotoBtn.addEventListener('click', () => photoInput.click());
    if (removePhotoBtn) removePhotoBtn.addEventListener('click', removePhoto);
    if (photoInput) photoInput.addEventListener('change', handlePhotoSelect);
    if (uploadDocumentBtn) uploadDocumentBtn.addEventListener('click', uploadDocument);

    // Event listeners pour les filtres
    if (filterNationality) filterNationality.addEventListener('change', applyFilters);
    if (filterSexe) filterSexe.addEventListener('change', applyFilters);

    // Event listeners pour import/export
    const exportBtn = document.getElementById('exportBtn');
    const importBtn = document.getElementById('importBtn');

    if (exportBtn) {
        exportBtn.addEventListener('click', exportClients);
    }

    if (importBtn) {
        importBtn.addEventListener('click', () => {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.csv,.json';
            input.onchange = handleImportFile;
            input.click();
        });
    }

    // Event listeners pour fermer les modals
    document.querySelectorAll('.close').forEach(closeBtn => {
        closeBtn.addEventListener('click', closeModal);
    });

    window.addEventListener('click', (e) => {
        if (e.target === clientModal) closeModal();
        if (e.target === deleteModal) closeDeleteModal();
    });

    // Recherche en temps réel
    if (searchInput) searchInput.addEventListener('input', debounce(searchClients, 300));

    // Charger les données
    await loadReferencesData();
    loadClients();
    updateStatistics();
    initializeTabs();

    // Event listeners pour la carte de fidélité
    const airlineSelect = document.getElementById('airline_code_for_loyalty_card_id');
    const cardNumberInput = document.getElementById('loyalty_card_no');

    if (airlineSelect) {
        airlineSelect.addEventListener('change', updateLoyaltyCardPreview);
    }

    if (cardNumberInput) {
        cardNumberInput.addEventListener('input', updateLoyaltyCardPreview);
    }

    // Event listener pour le bouton aperçu
    const previewBtn = document.getElementById('previewBtn');
    if (previewBtn) {
        previewBtn.addEventListener('click', showClientPreview);
    }

    // Initialiser la zone de drag & drop pour les documents
    initializeDocumentDragDrop();

    console.log('✅ Application initialisée avec tous les event listeners');
});

// Fonction pour initialiser les onglets
function initializeTabs() {
    console.log('🔧 Initialisation des onglets...');

    // Récupérer les éléments DOM
    tabButtons = document.querySelectorAll('.tab-btn');
    tabContents = document.querySelectorAll('.tab-content');

    console.log('📋 Boutons d\'onglets trouvés:', tabButtons.length);
    console.log('📋 Contenus d\'onglets trouvés:', tabContents.length);

    if (tabButtons.length === 0) {
        console.warn('⚠️ Aucun bouton d\'onglet trouvé');
        return;
    }

    // Ajouter les event listeners aux boutons d'onglets
    tabButtons.forEach((btn, index) => {
        console.log(`🔘 Bouton ${index + 1}: ${btn.dataset.tab}`);
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log('🖱️ Clic sur onglet:', btn.dataset.tab);
            switchTab(btn.dataset.tab);
        });
    });

    // S'assurer que l'onglet général est actif par défaut
    switchTab('general');
}

// Fonction pour charger toutes les données de référence
async function loadReferencesData() {
    try {
        const response = await fetch('/api/references/all');
        if (!response.ok) throw new Error('Erreur lors du chargement des références');

        referencesData = await response.json();
        populateSelectOptions();
        populateFilters();

        console.log('✅ Données de référence chargées:', referencesData);
    } catch (error) {
        console.error('❌ Erreur chargement références:', error);
        showError('Erreur lors du chargement des données de référence');
    }
}

// Fonction pour peupler les options des select
function populateSelectOptions() {
    // Nationalités
    populateSelect('nationality_id', referencesData.nationalities, 'nom_fr');

    // Sexes
    populateSelect('sexe_id', referencesData.sexes, 'libelle_fr');

    // Situations familiales
    populateSelect('situation_familiale_id', referencesData.situationsFamiliales, 'libelle_fr');

    // Pays de naissance
    populateSelect('pays_de_naissance_id', referencesData.paysNaissance, 'nom_fr');

    // Lieux de naissance
    populateSelect('lieu_de_naissance_id', referencesData.lieuxNaissance, 'nom_fr');

    // Labels téléphone
    populateSelect('label_tel_id', referencesData.labelsTel, 'libelle');

    // Labels mobile
    populateSelect('label_mobile_1_id', referencesData.labelsMobile, 'libelle');
    populateSelect('label_mobile_2_id', referencesData.labelsMobile, 'libelle');

    // Compagnies aériennes
    populateSelect('airline_code_for_loyalty_card_id', referencesData.airlineCodes, 'nom');

    // Labels nom apparenté
    populateSelect('label_related_name_id', referencesData.labelsRelatedName, 'libelle');

    // Comptes comptables
    populateSelect('compte_comptable_id', referencesData.comptesComptables, 'libelle');
}

// Fonction utilitaire pour peupler un select
function populateSelect(selectId, data, labelField) {
    const select = document.getElementById(selectId);
    if (!select || !data) return;

    // Garder l'option par défaut
    const defaultOption = select.querySelector('option[value=""]');
    select.innerHTML = '';
    if (defaultOption) select.appendChild(defaultOption);

    data.forEach(item => {
        const option = document.createElement('option');
        option.value = item.id;
        option.textContent = item[labelField];
        select.appendChild(option);
    });
}

// Fonction pour peupler les filtres
function populateFilters() {
    // Filtre nationalités
    populateSelect('filterNationality', referencesData.nationalities, 'nom_fr');

    // Filtre sexes
    populateSelect('filterSexe', referencesData.sexes, 'libelle_fr');
}

// Fonction pour changer d'onglet
function switchTab(tabName) {
    console.log('🔄 Changement d\'onglet vers:', tabName);
    currentTab = tabName;

    // Récupérer les éléments si pas encore fait
    if (tabButtons.length === 0) {
        tabButtons = document.querySelectorAll('.tab-btn');
        tabContents = document.querySelectorAll('.tab-content');
    }

    // Mettre à jour les boutons d'onglets
    tabButtons.forEach(btn => {
        if (btn.dataset.tab === tabName) {
            btn.classList.add('active');
        } else {
            btn.classList.remove('active');
        }
    });

    // Mettre à jour le contenu des onglets
    tabContents.forEach(content => {
        if (content.id === `tab-${tabName}`) {
            content.classList.add('active');
            content.style.display = 'block';
        } else {
            content.classList.remove('active');
            content.style.display = 'none';
        }
    });

    // Actions spéciales selon l'onglet
    if (tabName === 'documents' && isEditing && currentClientId) {
        loadClientDocuments(currentClientId);
    }

    console.log('✅ Onglet changé vers:', tabName);
}

// Fonction pour mettre à jour les statistiques
async function updateStatistics() {
    try {
        console.log('📊 Mise à jour des statistiques...');
        const response = await fetch('/api/clients');
        if (!response.ok) throw new Error('Erreur lors du chargement des clients');

        const clients = await response.json();
        console.log('📋 Clients récupérés:', clients.length);

        // Total des clients
        const totalClientsEl = document.getElementById('totalClients');
        if (totalClientsEl) {
            totalClientsEl.textContent = clients.length;
            console.log('✅ Total clients mis à jour:', clients.length);
        } else {
            console.warn('⚠️ Élément totalClients non trouvé');
        }

        // Clients de ce mois
        const now = new Date();
        const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

        const newThisMonth = clients.filter(client => {
            const createdDate = new Date(client.date_creation);
            return createdDate >= thisMonth;
        }).length;

        const newClientsMonthEl = document.getElementById('newClientsMonth');
        if (newClientsMonthEl) {
            newClientsMonthEl.textContent = newThisMonth;
            console.log('✅ Nouveaux clients ce mois mis à jour:', newThisMonth);
        } else {
            console.warn('⚠️ Élément newClientsMonth non trouvé');
        }

        console.log('📊 Statistiques mises à jour avec succès');

    } catch (error) {
        console.error('❌ Erreur mise à jour statistiques:', error);
    }
}

// Event listeners supprimés - maintenant dans DOMContentLoaded

// Fonction de debounce pour la recherche
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Charger tous les clients
async function loadClients() {
    try {
        console.log('🔄 Chargement des clients...');
        showLoading();

        const response = await fetch('/api/clients');
        console.log('📥 Réponse API:', response.status, response.statusText);

        if (!response.ok) throw new Error('Erreur lors du chargement');

        clients = await response.json();
        console.log('📋 Clients récupérés:', clients.length, clients);

        displayClients(clients);
        updateStatistics();
        console.log('✅ Chargement terminé');
    } catch (error) {
        console.error('❌ Erreur loadClients:', error);
        showError('Erreur lors du chargement des clients: ' + error.message);
    }
}

// Rechercher des clients
async function searchClients() {
    const searchTerm = searchInput.value.trim();
    
    if (!searchTerm) {
        loadClients();
        return;
    }

    try {
        showLoading();
        const response = await fetch(`/api/clients/search?q=${encodeURIComponent(searchTerm)}`);
        if (!response.ok) throw new Error('Erreur lors de la recherche');
        
        const searchResults = await response.json();
        displayClients(searchResults);
    } catch (error) {
        showError('Erreur lors de la recherche: ' + error.message);
    }
}

// Effacer la recherche
function clearSearch() {
    searchInput.value = '';
    loadClients();
}

// Afficher les clients
function displayClients(clientsList) {
    console.log('🖥️ displayClients appelé avec:', clientsList.length, 'clients');
    console.log('📋 Container element:', clientsContainer);

    if (!clientsContainer) {
        console.error('❌ clientsContainer non trouvé !');
        return;
    }

    if (clientsList.length === 0) {
        console.log('ℹ️ Aucun client à afficher');
        clientsContainer.innerHTML = `
            <div class="no-clients">
                <h3>Aucun client trouvé</h3>
                <p>Commencez par ajouter votre premier client !</p>
            </div>
        `;
        return;
    }

    console.log('✅ Affichage de', clientsList.length, 'clients');

    clientsContainer.innerHTML = clientsList.map(client => `
        <div class="client-card">
            <div class="client-photo">
                ${client.photo_url ?
                    `<img src="${client.photo_url}" alt="Photo de ${client.nom} ${client.prenom}">` :
                    '<div class="no-photo"><i class="fas fa-user"></i></div>'
                }
            </div>
            <div class="client-content">
                <div class="client-header">
                    <div>
                        <div class="client-name">${client.nom} ${client.prenom}</div>
                        ${client.code_client ? `<div class="client-code">${client.code_client}</div>` : ''}
                    </div>
                    <div class="client-actions">
                        <button class="btn-edit" onclick="editClient(${client.id})">
                            <i class="fas fa-edit"></i> Modifier
                        </button>
                        <button class="btn-delete" onclick="deleteClient(${client.id})">
                            <i class="fas fa-trash"></i> Supprimer
                        </button>
                    </div>
                </div>
                <div class="client-info">
                    <div class="info-item">
                        <span class="info-label"><i class="fas fa-envelope"></i> Email:</span>
                        <span class="info-value">${client.email}</span>
                    </div>
                    ${client.telephone ? `
                        <div class="info-item">
                            <span class="info-label"><i class="fas fa-phone"></i> Téléphone:</span>
                            <span class="info-value">${client.telephone}</span>
                        </div>
                    ` : ''}
                    ${client.mobile_1 ? `
                        <div class="info-item">
                            <span class="info-label"><i class="fas fa-mobile-alt"></i> Mobile:</span>
                            <span class="info-value">${client.mobile_1}</span>
                        </div>
                    ` : ''}
                    ${client.nationality_name ? `
                        <div class="info-item">
                            <span class="info-label"><i class="fas fa-flag"></i> Nationalité:</span>
                            <span class="info-value">${client.nationality_name}</span>
                        </div>
                    ` : ''}
                    ${client.date_de_naissance ? `
                        <div class="info-item">
                            <span class="info-label"><i class="fas fa-birthday-cake"></i> Âge:</span>
                            <span class="info-value">${calculateAge(client.date_de_naissance)} ans</span>
                        </div>
                    ` : ''}
                    ${client.ville ? `
                        <div class="info-item">
                            <span class="info-label"><i class="fas fa-map-marker-alt"></i> Ville:</span>
                            <span class="info-value">${client.ville} ${client.code_postal || ''}</span>
                        </div>
                    ` : ''}
                    <div class="info-item">
                        <span class="info-label"><i class="fas fa-calendar-plus"></i> Créé:</span>
                        <span class="info-value">${formatDate(client.date_creation)}</span>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

// Ouvrir le modal d'ajout
function openAddModal() {
    console.log('➕ Ouverture modal ajout client');

    isEditing = false;
    currentClientId = null;
    existingPhotoUrl = null;
    modalTitle.textContent = 'Nouveau Client';
    clientForm.reset();
    resetPhotoPreview();

    // Masquer la section documents pour les nouveaux clients
    const documentsTab = document.querySelector('[data-tab="documents"]');
    if (documentsTab) {
        documentsTab.style.display = 'none';
    }

    // Afficher le modal puis initialiser les onglets
    clientModal.style.display = 'block';

    // Attendre que le modal soit visible puis initialiser les onglets
    setTimeout(() => {
        switchTab('general');
    }, 100);
}

// Modifier un client
async function editClient(id) {
    try {
        const response = await fetch(`/api/clients/${id}`);
        if (!response.ok) throw new Error('Client non trouvé');
        
        const client = await response.json();
        
        isEditing = true;
        currentClientId = id;
        modalTitle.textContent = 'Modifier le Client';
        
        // Remplir le formulaire - Onglet Général
        document.getElementById('nom').value = client.nom || '';
        document.getElementById('prenom').value = client.prenom || '';
        document.getElementById('email').value = client.email || '';
        document.getElementById('nom_arabe').value = client.nom_arabe || '';
        document.getElementById('prenom_arabe').value = client.prenom_arabe || '';
        document.getElementById('sexe_id').value = client.sexe_id || '';
        document.getElementById('date_de_naissance').value = client.date_de_naissance || '';
        document.getElementById('nationality_id').value = client.nationality_id || '';
        document.getElementById('situation_familiale_id').value = client.situation_familiale_id || '';

        // Onglet Identité
        document.getElementById('cin_no').value = client.cin_no || '';
        document.getElementById('passeport_no').value = client.passeport_no || '';
        document.getElementById('date_expiration_passeport').value = client.date_expiration_passeport || '';
        document.getElementById('pays_de_naissance_id').value = client.pays_de_naissance_id || '';
        document.getElementById('lieu_de_naissance_id').value = client.lieu_de_naissance_id || '';
        document.getElementById('compte_comptable_id').value = client.compte_comptable_id || '';

        // Onglet Contact
        document.getElementById('adresse').value = client.adresse || '';
        document.getElementById('ville').value = client.ville || '';
        document.getElementById('code_postal').value = client.code_postal || '';
        document.getElementById('telephone').value = client.telephone || '';
        document.getElementById('label_tel_id').value = client.label_tel_id || '';
        document.getElementById('mobile_1').value = client.mobile_1 || '';
        document.getElementById('label_mobile_1_id').value = client.label_mobile_1_id || '';
        document.getElementById('mobile_2').value = client.mobile_2 || '';
        document.getElementById('label_mobile_2_id').value = client.label_mobile_2_id || '';
        document.getElementById('related_name').value = client.related_name || '';
        document.getElementById('label_related_name_id').value = client.label_related_name_id || '';
        document.getElementById('tel_related_name').value = client.tel_related_name || '';

        // Onglet Voyage
        document.getElementById('airline_code_for_loyalty_card_id').value = client.airline_code_for_loyalty_card_id || '';
        document.getElementById('loyalty_card_no').value = client.loyalty_card_no || '';

        // Afficher la photo existante
        existingPhotoUrl = client.photo_url;
        if (client.photo_url) {
            previewImage.src = client.photo_url;
            previewImage.style.display = 'block';
            document.querySelector('.photo-placeholder').style.display = 'none';
            removePhotoBtn.style.display = 'inline-block';
        } else {
            resetPhotoPreview();
        }

        // Afficher l'onglet documents pour les clients existants
        const documentsTab = document.querySelector('[data-tab="documents"]');
        if (documentsTab) {
            documentsTab.style.display = 'flex';
        }

        // Afficher le modal
        clientModal.style.display = 'block';

        // Attendre que le modal soit visible puis initialiser les onglets
        setTimeout(() => {
            switchTab('general');
            // Mettre à jour la preview de la carte de fidélité si applicable
            updateLoyaltyCardPreview();
        }, 100);
    } catch (error) {
        showError('Erreur lors du chargement du client: ' + error.message);
    }
}

// Supprimer un client
function deleteClient(id) {
    currentClientId = id;
    deleteModal.style.display = 'block';
}

// Confirmer la suppression
async function confirmDelete() {
    try {
        const response = await fetch(`/api/clients/${currentClientId}`, {
            method: 'DELETE'
        });
        
        if (!response.ok) throw new Error('Erreur lors de la suppression');
        
        showSuccess('Client supprimé avec succès');
        closeDeleteModal();
        loadClients();
    } catch (error) {
        showError('Erreur lors de la suppression: ' + error.message);
    }
}

// Gérer la soumission du formulaire
async function handleFormSubmit(e) {
    e.preventDefault();
    console.log('📝 Soumission du formulaire...');

    try {
        // Récupérer les données du formulaire manuellement pour éviter les problèmes d'encodage
        const clientData = {};

        // Champs de base
        clientData.nom = document.getElementById('nom')?.value?.trim() || null;
        clientData.prenom = document.getElementById('prenom')?.value?.trim() || null;
        clientData.email = document.getElementById('email')?.value?.trim() || null;
        clientData.telephone = document.getElementById('telephone')?.value?.trim() || null;
        clientData.adresse = document.getElementById('adresse')?.value?.trim() || null;
        clientData.ville = document.getElementById('ville')?.value?.trim() || null;
        clientData.code_postal = document.getElementById('code_postal')?.value?.trim() || null;

        // Nouveaux champs - Identité
        clientData.cin_no = document.getElementById('cin_no')?.value?.trim() || null;
        clientData.passeport_no = document.getElementById('passeport_no')?.value?.trim() || null;
        clientData.date_expiration_passeport = document.getElementById('date_expiration_passeport')?.value || null;
        clientData.nationality_id = document.getElementById('nationality_id')?.value || null;
        clientData.sexe_id = document.getElementById('sexe_id')?.value || null;
        clientData.situation_familiale_id = document.getElementById('situation_familiale_id')?.value || null;
        clientData.date_de_naissance = document.getElementById('date_de_naissance')?.value || null;
        clientData.pays_de_naissance_id = document.getElementById('pays_de_naissance_id')?.value || null;
        clientData.lieu_de_naissance_id = document.getElementById('lieu_de_naissance_id')?.value || null;
        clientData.compte_comptable_id = document.getElementById('compte_comptable_id')?.value || null;

        // Nouveaux champs - Contact
        clientData.label_tel_id = document.getElementById('label_tel_id')?.value || null;
        clientData.mobile_1 = document.getElementById('mobile_1')?.value?.trim() || null;
        clientData.label_mobile_1_id = document.getElementById('label_mobile_1_id')?.value || null;
        clientData.mobile_2 = document.getElementById('mobile_2')?.value?.trim() || null;
        clientData.label_mobile_2_id = document.getElementById('label_mobile_2_id')?.value || null;
        clientData.related_name = document.getElementById('related_name')?.value?.trim() || null;
        clientData.label_related_name_id = document.getElementById('label_related_name_id')?.value || null;
        clientData.tel_related_name = document.getElementById('tel_related_name')?.value?.trim() || null;

        // Nouveaux champs - Voyage
        clientData.airline_code_for_loyalty_card_id = document.getElementById('airline_code_for_loyalty_card_id')?.value || null;
        clientData.loyalty_card_no = document.getElementById('loyalty_card_no')?.value?.trim() || null;

        // Nouveaux champs - Multilingue (attention aux caractères spéciaux)
        const nomArabe = document.getElementById('nom_arabe')?.value?.trim();
        const prenomArabe = document.getElementById('prenom_arabe')?.value?.trim();
        clientData.nom_arabe = nomArabe || null;
        clientData.prenom_arabe = prenomArabe || null;

        // Convertir les IDs en nombres
        ['nationality_id', 'sexe_id', 'situation_familiale_id', 'pays_de_naissance_id',
         'lieu_de_naissance_id', 'label_tel_id', 'label_mobile_1_id', 'label_mobile_2_id',
         'airline_code_for_loyalty_card_id', 'label_related_name_id', 'compte_comptable_id'].forEach(field => {
            if (clientData[field]) {
                clientData[field] = parseInt(clientData[field]) || null;
            }
        });

        console.log('📋 Données nettoyées:', clientData);

        // Validation des champs obligatoires
        if (!clientData.nom || !clientData.prenom || !clientData.email) {
            throw new Error('Les champs Nom, Prénom et Email sont obligatoires');
        }

        // Gestion de la photo en mode édition
        if (isEditing) {
            if (currentPhotoFile) {
                console.log('📸 Nouvelle photo sera uploadée');
            } else if (existingPhotoUrl) {
                clientData.photo_url = existingPhotoUrl;
                console.log('🖼️ Conservation de la photo existante');
            } else {
                clientData.photo_url = null;
                console.log('🗑️ Photo supprimée ou aucune photo');
            }
        }

        // Étape 1: Sauvegarder/mettre à jour le client
        const url = isEditing ? `/api/clients/${currentClientId}` : '/api/clients';
        const method = isEditing ? 'PUT' : 'POST';

        console.log('🌐 Envoi requête:', { url, method });
        console.log('📋 Données à envoyer:', JSON.stringify(clientData, null, 2));

        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json; charset=utf-8'
            },
            body: JSON.stringify(clientData)
        });

        console.log('📥 Réponse reçue:', response.status, response.statusText);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('❌ Erreur réponse:', errorText);
            let errorMessage;
            try {
                const errorJson = JSON.parse(errorText);
                errorMessage = errorJson.error || 'Erreur lors de l\'enregistrement';
            } catch {
                errorMessage = `Erreur ${response.status}: ${errorText}`;
            }
            throw new Error(errorMessage);
        }

        const result = await response.json();
        console.log('✅ Résultat:', result);
        const clientId = isEditing ? currentClientId : result.id;

        // Étape 2: Upload de la photo si une nouvelle photo a été sélectionnée
        console.log('🔍 Vérification photo:', { currentPhotoFile, clientId });
        if (currentPhotoFile) {
            console.log('📸 Upload de la nouvelle photo...');
            try {
                await uploadPhoto(clientId);
            } catch (photoError) {
                console.warn('⚠️ Erreur upload photo:', photoError.message);
                // Ne pas faire échouer toute la sauvegarde pour une erreur de photo
            }
        } else {
            console.log('ℹ️ Pas de nouvelle photo à uploader');
        }

        const message = isEditing ? 'Client modifié avec succès ✅' : 'Client ajouté avec succès ✅';
        showSuccess(message);
        closeModal();
        loadClients();
        updateStatistics();

    } catch (error) {
        console.error('❌ Erreur complète:', error);
        showError(`Erreur: ${error.message}`);
    }
}

// Fonction pour uploader une photo
async function uploadPhoto(clientId) {
    console.log('🔄 Upload photo pour client ID:', clientId);
    console.log('📁 Fichier photo:', currentPhotoFile);

    const formData = new FormData();
    formData.append('photo', currentPhotoFile);

    console.log('📤 Envoi de la photo...');
    const response = await fetch(`/api/upload/photo/${clientId}`, {
        method: 'POST',
        body: formData
    });

    console.log('📥 Réponse reçue:', response.status);

    if (!response.ok) {
        const error = await response.json();
        console.error('❌ Erreur upload photo:', error);
        throw new Error('Erreur lors de l\'upload de la photo: ' + error.error);
    }

    // Récupérer l'URL de la nouvelle photo
    const result = await response.json();
    console.log('✅ Photo uploadée avec succès:', result);
    existingPhotoUrl = result.photoUrl;

    currentPhotoFile = null;
}

// Fermer le modal principal
function closeModal() {
    clientModal.style.display = 'none';
    clientForm.reset();
    resetPhotoPreview();
    currentClientId = null;
    isEditing = false;
    currentPhotoFile = null;
    existingPhotoUrl = null;
    documentsSection.style.display = 'none';
}

// Fermer le modal de suppression
function closeDeleteModal() {
    deleteModal.style.display = 'none';
    currentClientId = null;
}

// Fonction pour afficher l'aperçu du client
function showClientPreview() {
    console.log('👁️ Affichage aperçu client...');

    // Récupérer les données du formulaire
    const formData = new FormData(clientForm);
    const clientData = Object.fromEntries(formData.entries());

    // Créer le contenu de l'aperçu
    let previewContent = `
        <div class="client-preview">
            <h3>Aperçu du Client</h3>
            <div class="preview-section">
                <h4>Informations Générales</h4>
                <p><strong>Nom:</strong> ${clientData.nom || 'Non renseigné'}</p>
                <p><strong>Prénom:</strong> ${clientData.prenom || 'Non renseigné'}</p>
                <p><strong>Email:</strong> ${clientData.email || 'Non renseigné'}</p>
                <p><strong>Téléphone:</strong> ${clientData.telephone || 'Non renseigné'}</p>
            </div>

            <div class="preview-section">
                <h4>Contact</h4>
                <p><strong>Adresse:</strong> ${clientData.adresse || 'Non renseigné'}</p>
                <p><strong>Ville:</strong> ${clientData.ville || 'Non renseigné'}</p>
                <p><strong>Code Postal:</strong> ${clientData.code_postal || 'Non renseigné'}</p>
                <p><strong>Mobile 1:</strong> ${clientData.mobile_1 || 'Non renseigné'}</p>
                <p><strong>Mobile 2:</strong> ${clientData.mobile_2 || 'Non renseigné'}</p>
            </div>

            <div class="preview-section">
                <h4>Identité</h4>
                <p><strong>N° CIN:</strong> ${clientData.cin_no || 'Non renseigné'}</p>
                <p><strong>N° Passeport:</strong> ${clientData.passeport_no || 'Non renseigné'}</p>
                <p><strong>Date de naissance:</strong> ${clientData.date_de_naissance || 'Non renseigné'}</p>
            </div>
        </div>
    `;

    // Créer et afficher le modal d'aperçu
    const previewModal = document.createElement('div');
    previewModal.className = 'modal';
    previewModal.style.display = 'block';
    previewModal.innerHTML = `
        <div class="modal-content" style="max-width: 600px;">
            <div class="modal-header">
                <h3>Aperçu Client</h3>
                <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
            </div>
            <div class="modal-body">
                ${previewContent}
            </div>
            <div class="modal-actions">
                <button type="button" class="btn-secondary" onclick="this.closest('.modal').remove()">
                    <i class="fas fa-times"></i> Fermer
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(previewModal);

    // Fermer en cliquant en dehors
    previewModal.addEventListener('click', (e) => {
        if (e.target === previewModal) {
            previewModal.remove();
        }
    });
}

// Afficher le chargement
function showLoading() {
    clientsContainer.innerHTML = '<div class="loading">Chargement des clients...</div>';
}

// Cette fonction est supprimée car dupliquée plus bas

// Afficher un succès
function showSuccess(message) {
    const successDiv = document.createElement('div');
    successDiv.className = 'success-message';
    successDiv.textContent = message;
    
    document.querySelector('.container').insertBefore(successDiv, document.querySelector('.clients-section'));
    
    setTimeout(() => successDiv.remove(), 3000);
}

// Formater une date
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

// Fonctions pour gérer les photos
function handlePhotoSelect(e) {
    const file = e.target.files[0];
    if (file) {
        currentPhotoFile = file;
        const reader = new FileReader();
        reader.onload = (e) => {
            previewImage.src = e.target.result;
            previewImage.style.display = 'block';
            document.querySelector('.photo-placeholder').style.display = 'none';
            removePhotoBtn.style.display = 'inline-block';
        };
        reader.readAsDataURL(file);
    }
}

function removePhoto() {
    console.log('🗑️ Suppression de la photo');
    currentPhotoFile = null;

    // Si on est en mode édition, on marque la photo pour suppression
    if (isEditing) {
        existingPhotoUrl = null; // Cela va supprimer la photo existante lors de la sauvegarde
    }

    resetPhotoPreview();
}

function resetPhotoPreview() {
    previewImage.src = '';
    previewImage.style.display = 'none';
    document.querySelector('.photo-placeholder').style.display = 'block';
    removePhotoBtn.style.display = 'none';
    photoInput.value = '';
}

// Fonction pour initialiser le drag & drop des documents
function initializeDocumentDragDrop() {
    const uploadZone = document.getElementById('uploadZone');
    const documentInput = document.getElementById('documentInput');

    if (!uploadZone || !documentInput) {
        console.warn('⚠️ Zone de drag & drop non trouvée');
        return;
    }

    console.log('🔧 Initialisation drag & drop documents...');

    // Event listeners pour le drag & drop
    uploadZone.addEventListener('click', () => {
        documentInput.click();
    });

    uploadZone.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadZone.classList.add('dragover');
    });

    uploadZone.addEventListener('dragleave', (e) => {
        e.preventDefault();
        uploadZone.classList.remove('dragover');
    });

    uploadZone.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadZone.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            documentInput.files = files;
            // Déclencher l'upload automatiquement
            if (currentClientId) {
                uploadDocument();
            } else {
                showError('Veuillez d\'abord sélectionner un client');
            }
        }
    });

    // Event listener pour la sélection de fichier
    documentInput.addEventListener('change', (e) => {
        if (e.target.files.length > 0 && currentClientId) {
            uploadDocument();
        }
    });

    console.log('✅ Drag & drop documents initialisé');
}

// Fonctions pour gérer les documents
async function loadClientDocuments(clientId) {
    try {
        console.log('📄 Chargement documents pour client:', clientId);
        const response = await fetch(`/api/upload/documents/${clientId}`);
        if (!response.ok) throw new Error('Erreur lors du chargement des documents');

        clientDocuments = await response.json();
        console.log('📋 Documents récupérés:', clientDocuments.length);
        displayDocuments();
    } catch (error) {
        console.error('❌ Erreur chargement documents:', error);
        documentsContainer.innerHTML = '<div class="error">Erreur lors du chargement des documents</div>';
    }
}

function displayDocuments() {
    if (clientDocuments.length === 0) {
        documentsContainer.innerHTML = '<div class="no-documents">Aucun document ajouté</div>';
        return;
    }

    documentsContainer.innerHTML = clientDocuments.map(doc => `
        <div class="document-item">
            <div class="document-info">
                <span class="document-type">${getDocumentTypeLabel(doc.type_document)}</span>
                <span class="document-name">${doc.nom_fichier}</span>
                <span class="document-size">(${formatFileSize(doc.taille_fichier)})</span>
            </div>
            <div class="document-actions">
                <button class="btn-download" onclick="downloadDocument(${doc.id})">⬇️ Télécharger</button>
                <button class="btn-delete" onclick="deleteDocument(${doc.id})">🗑️ Supprimer</button>
            </div>
        </div>
    `).join('');
}

async function uploadDocument() {
    const files = documentInput.files;
    if (!files || files.length === 0) {
        showError('Veuillez sélectionner un fichier');
        return;
    }

    if (!currentClientId) {
        showError('Erreur: ID client non trouvé');
        return;
    }

    console.log('📤 Upload de', files.length, 'document(s) pour client:', currentClientId);

    // Upload de chaque fichier
    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        console.log(`📄 Upload fichier ${i + 1}/${files.length}:`, file.name);

        const formData = new FormData();
        formData.append('document', file);
        formData.append('type_document', documentType.value);

        try {
            const response = await fetch(`/api/upload/document/${currentClientId}`, {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error || 'Erreur lors de l\'upload');
            }

            const result = await response.json();
            console.log('✅ Document uploadé:', result);

        } catch (error) {
            console.error('❌ Erreur upload fichier:', file.name, error);
            showError(`Erreur upload ${file.name}: ${error.message}`);
            return; // Arrêter en cas d'erreur
        }
    }

    showSuccess(`${files.length} document(s) ajouté(s) avec succès`);
    documentInput.value = '';
    loadClientDocuments(currentClientId);
}

async function deleteDocument(documentId) {
    if (!confirm('Êtes-vous sûr de vouloir supprimer ce document ?')) return;

    try {
        const response = await fetch(`/api/upload/document/${documentId}`, {
            method: 'DELETE'
        });

        if (!response.ok) throw new Error('Erreur lors de la suppression');

        showSuccess('Document supprimé avec succès');
        loadClientDocuments(currentClientId);
    } catch (error) {
        showError('Erreur lors de la suppression: ' + error.message);
    }
}

function downloadDocument(documentId) {
    window.open(`/api/upload/document/${documentId}/download`, '_blank');
}

function getDocumentTypeLabel(type) {
    const types = {
        'passeport': '🛂 Passeport',
        'carte_identite': '🆔 Carte d\'identité',
        'permis_conduire': '🚗 Permis de conduire',
        'autre': '📄 Autre'
    };
    return types[type] || '📄 Document';
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Fonction pour calculer l'âge
function calculateAge(birthDate) {
    if (!birthDate) return '';

    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
        age--;
    }

    return age;
}

// Fonction pour mettre à jour la preview de la carte de fidélité
function updateLoyaltyCardPreview() {
    const airlineSelect = document.getElementById('airline_code_for_loyalty_card_id');
    const cardNumberInput = document.getElementById('loyalty_card_no');
    const loyaltyPreview = document.getElementById('loyaltyPreview');

    if (!airlineSelect || !cardNumberInput || !loyaltyPreview) return;

    const airlineId = airlineSelect.value;
    const cardNumber = cardNumberInput.value;

    if (airlineId && cardNumber && referencesData.airlineCodes) {
        const airline = referencesData.airlineCodes.find(a => a.id == airlineId);
        if (airline) {
            document.getElementById('airlineName').textContent = airline.nom;
            document.getElementById('cardNumber').textContent = cardNumber;
            document.getElementById('cardHolder').textContent =
                `${document.getElementById('nom').value} ${document.getElementById('prenom').value}`.trim();
            loyaltyPreview.style.display = 'block';
        }
    } else {
        loyaltyPreview.style.display = 'none';
    }
}

// Fonction pour appliquer les filtres
function applyFilters() {
    const nationalityFilter = filterNationality.value;
    const sexeFilter = filterSexe.value;

    let filteredClients = [...clients];

    if (nationalityFilter) {
        filteredClients = filteredClients.filter(client =>
            client.nationality_id == nationalityFilter
        );
    }

    if (sexeFilter) {
        filteredClients = filteredClients.filter(client =>
            client.sexe_id == sexeFilter
        );
    }

    displayClients(filteredClients);
}

// Fonctions pour afficher les messages
function showError(message) {
    console.error('❌ Erreur:', message);

    // Supprimer les anciens messages
    const existingMessages = document.querySelectorAll('.error-message, .success-message');
    existingMessages.forEach(msg => msg.remove());

    // Créer le message d'erreur
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;

    // Insérer au début du container
    const container = document.querySelector('.container');
    if (container) {
        container.insertBefore(errorDiv, container.firstChild);

        // Supprimer automatiquement après 5 secondes
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.remove();
            }
        }, 5000);
    } else {
        alert('Erreur: ' + message);
    }
}

function showSuccess(message) {
    console.log('✅ Succès:', message);

    // Supprimer les anciens messages
    const existingMessages = document.querySelectorAll('.error-message, .success-message');
    existingMessages.forEach(msg => msg.remove());

    // Créer le message de succès
    const successDiv = document.createElement('div');
    successDiv.className = 'success-message';
    successDiv.innerHTML = `<i class="fas fa-check-circle"></i> ${message}`;

    // Insérer au début du container
    const container = document.querySelector('.container');
    if (container) {
        container.insertBefore(successDiv, container.firstChild);

        // Supprimer automatiquement après 3 secondes
        setTimeout(() => {
            if (successDiv.parentNode) {
                successDiv.remove();
            }
        }, 3000);
    }
}

// Fonction pour exporter les clients
async function exportClients() {
    try {
        console.log('📤 Début de l\'export...');
        showSuccess('Préparation de l\'export...');

        const response = await fetch('/api/clients');
        if (!response.ok) throw new Error('Erreur lors de la récupération des clients');

        const clients = await response.json();
        console.log('📋 Clients à exporter:', clients.length);

        // Créer le CSV
        const csvContent = createCSVContent(clients);

        // Créer et télécharger le fichier
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);

        link.setAttribute('href', url);
        link.setAttribute('download', `clients_export_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        showSuccess(`Export réussi ! ${clients.length} clients exportés.`);
        console.log('✅ Export terminé avec succès');

    } catch (error) {
        console.error('❌ Erreur export:', error);
        showError('Erreur lors de l\'export: ' + error.message);
    }
}

// Fonction pour créer le contenu CSV
function createCSVContent(clients) {
    const headers = [
        'Code Client', 'Nom', 'Prénom', 'Email', 'Téléphone', 'Mobile 1', 'Mobile 2',
        'Adresse', 'Ville', 'Code Postal', 'CIN', 'Passeport', 'Date Naissance',
        'Nationalité', 'Sexe', 'Situation Familiale', 'Nom Arabe', 'Prénom Arabe',
        'Carte Fidélité', 'Contact Urgence', 'Tel Urgence', 'Date Création'
    ];

    let csvContent = headers.join(',') + '\n';

    clients.forEach(client => {
        const row = [
            client.code_client || '',
            `"${client.nom || ''}"`,
            `"${client.prenom || ''}"`,
            client.email || '',
            client.telephone || '',
            client.mobile_1 || '',
            client.mobile_2 || '',
            `"${client.adresse || ''}"`,
            `"${client.ville || ''}"`,
            client.code_postal || '',
            client.cin_no || '',
            client.passeport_no || '',
            client.date_de_naissance || '',
            client.nationality_name || '',
            client.sexe_name || '',
            client.situation_familiale_name || '',
            `"${client.nom_arabe || ''}"`,
            `"${client.prenom_arabe || ''}"`,
            client.loyalty_card_no || '',
            `"${client.related_name || ''}"`,
            client.tel_related_name || '',
            client.date_creation || ''
        ];
        csvContent += row.join(',') + '\n';
    });

    return csvContent;
}

// Fonction pour gérer l'import de fichier
async function handleImportFile(event) {
    const file = event.target.files[0];
    if (!file) return;

    console.log('📥 Début de l\'import:', file.name);
    showSuccess('Lecture du fichier...');

    try {
        const text = await file.text();
        let clients = [];

        if (file.name.endsWith('.csv')) {
            clients = parseCSV(text);
        } else if (file.name.endsWith('.json')) {
            clients = JSON.parse(text);
        } else {
            throw new Error('Format de fichier non supporté. Utilisez CSV ou JSON.');
        }

        console.log('📋 Clients à importer:', clients.length);

        if (clients.length === 0) {
            throw new Error('Aucun client trouvé dans le fichier.');
        }

        // Confirmer l'import
        const confirmed = confirm(`Voulez-vous importer ${clients.length} clients ? Cette action ajoutera de nouveaux clients.`);
        if (!confirmed) return;

        // Importer les clients
        await importClients(clients);

    } catch (error) {
        console.error('❌ Erreur import:', error);
        showError('Erreur lors de l\'import: ' + error.message);
    }
}

// Fonction pour parser le CSV
function parseCSV(text) {
    const lines = text.split('\n').filter(line => line.trim());
    if (lines.length < 2) return [];

    const headers = lines[0].split(',').map(h => h.trim());
    const clients = [];

    for (let i = 1; i < lines.length; i++) {
        const values = parseCSVLine(lines[i]);
        if (values.length >= 3) { // Au minimum nom, prénom, email
            const client = {
                nom: values[1]?.replace(/"/g, '') || '',
                prenom: values[2]?.replace(/"/g, '') || '',
                email: values[3] || '',
                telephone: values[4] || '',
                mobile_1: values[5] || '',
                mobile_2: values[6] || '',
                adresse: values[7]?.replace(/"/g, '') || '',
                ville: values[8]?.replace(/"/g, '') || '',
                code_postal: values[9] || '',
                cin_no: values[10] || '',
                passeport_no: values[11] || '',
                date_de_naissance: values[12] || '',
                nom_arabe: values[16]?.replace(/"/g, '') || '',
                prenom_arabe: values[17]?.replace(/"/g, '') || '',
                loyalty_card_no: values[18] || '',
                related_name: values[19]?.replace(/"/g, '') || '',
                tel_related_name: values[20] || ''
            };

            // Nettoyer les valeurs vides
            Object.keys(client).forEach(key => {
                if (client[key] === '') client[key] = null;
            });

            clients.push(client);
        }
    }

    return clients;
}

// Fonction pour parser une ligne CSV (gère les guillemets)
function parseCSVLine(line) {
    const result = [];
    let current = '';
    let inQuotes = false;

    for (let i = 0; i < line.length; i++) {
        const char = line[i];

        if (char === '"') {
            inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
            result.push(current);
            current = '';
        } else {
            current += char;
        }
    }

    result.push(current);
    return result;
}

// Fonction pour importer les clients
async function importClients(clients) {
    let imported = 0;
    let errors = 0;

    showSuccess(`Import en cours... 0/${clients.length}`);

    for (let i = 0; i < clients.length; i++) {
        try {
            const response = await fetch('/api/clients', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(clients[i])
            });

            if (response.ok) {
                imported++;
            } else {
                errors++;
                console.warn(`⚠️ Erreur import client ${i + 1}:`, await response.text());
            }

            // Mettre à jour le progress
            if ((i + 1) % 5 === 0 || i === clients.length - 1) {
                showSuccess(`Import en cours... ${i + 1}/${clients.length}`);
            }

        } catch (error) {
            errors++;
            console.error(`❌ Erreur import client ${i + 1}:`, error);
        }
    }

    // Résultat final
    if (imported > 0) {
        showSuccess(`Import terminé ! ${imported} clients importés${errors > 0 ? `, ${errors} erreurs` : ''}.`);
        loadClients(); // Recharger la liste
        updateStatistics(); // Mettre à jour les stats
    } else {
        showError('Aucun client n\'a pu être importé.');
    }

    console.log(`✅ Import terminé: ${imported} succès, ${errors} erreurs`);
}


