<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 Test Final Complet</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        .result { margin: 15px 0; padding: 20px; border-radius: 8px; font-family: 'Courier New', monospace; white-space: pre-wrap; max-height: 400px; overflow-y: auto; }
        .success { background: #d4edda; color: #155724; border: 2px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 2px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 2px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 2px solid #ffeaa7; }
        button { padding: 15px 25px; margin: 10px; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; font-size: 16px; transition: all 0.3s ease; }
        .btn-primary { background: #007bff; color: white; }
        .btn-primary:hover { background: #0056b3; transform: translateY(-2px); }
        .btn-success { background: #28a745; color: white; }
        .btn-success:hover { background: #1e7e34; transform: translateY(-2px); }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        h1 { color: #333; text-align: center; margin-bottom: 30px; font-size: 2.5rem; }
        .test-section { margin: 25px 0; padding: 25px; border: 2px solid #e0e0e0; border-radius: 12px; background: #f8f9fa; }
        .progress { width: 100%; height: 25px; background: #e9ecef; border-radius: 15px; overflow: hidden; margin: 20px 0; }
        .progress-bar { height: 100%; background: linear-gradient(90deg, #007bff, #28a745); transition: width 0.5s ease; border-radius: 15px; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
        .stat-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 12px; text-align: center; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
        .stat-number { font-size: 2.5rem; font-weight: bold; margin-bottom: 5px; }
        .stat-label { font-size: 1rem; opacity: 0.9; }
        .test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .test-card { background: white; padding: 20px; border-radius: 10px; border: 1px solid #dee2e6; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .status-ok { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-pending { background: #ffc107; }
        .status-unknown { background: #6c757d; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Test Final Complet - Interface Corrigée</h1>
        
        <!-- Progress Bar -->
        <div class="progress">
            <div class="progress-bar" id="progressBar" style="width: 0%"></div>
        </div>
        <div style="text-align: center; margin-bottom: 30px;">
            <span id="progressText">Prêt à commencer les tests</span>
        </div>

        <!-- Statistiques -->
        <div class="test-section">
            <h3>📊 État du Système</h3>
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number" id="totalClients">-</div>
                    <div class="stat-label">Clients Total</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="testsRun">0</div>
                    <div class="stat-label">Tests Exécutés</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="testsSuccess">0</div>
                    <div class="stat-label">Tests Réussis</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="testsFailed">0</div>
                    <div class="stat-label">Tests Échoués</div>
                </div>
            </div>
        </div>

        <!-- Tests Principaux -->
        <div class="test-section">
            <h3>🚀 Tests Automatisés</h3>
            <div style="text-align: center; margin-bottom: 20px;">
                <button class="btn-success" onclick="runAllTests()" id="runAllBtn" style="font-size: 18px; padding: 20px 40px;">
                    🎯 LANCER TOUS LES TESTS
                </button>
            </div>
            <div id="mainTestResult" class="result"></div>
        </div>

        <!-- Tests Spécifiques -->
        <div class="test-section">
            <h3>🎯 Tests Spécifiques</h3>
            <div class="test-grid">
                <div class="test-card">
                    <h4><span class="status-indicator status-unknown" id="statusAPI"></span>API Backend</h4>
                    <button class="btn-primary" onclick="testAPI()">🔍 Tester API</button>
                    <div id="apiResult" class="result"></div>
                </div>
                <div class="test-card">
                    <h4><span class="status-indicator status-unknown" id="statusInterface"></span>Interface Web</h4>
                    <button class="btn-warning" onclick="testInterface()">🖥️ Tester Interface</button>
                    <div id="interfaceResult" class="result"></div>
                </div>
                <div class="test-card">
                    <h4><span class="status-indicator status-unknown" id="statusAjout"></span>Ajout Client</h4>
                    <button class="btn-success" onclick="testAddClient()">➕ Tester Ajout</button>
                    <div id="addResult" class="result"></div>
                </div>
                <div class="test-card">
                    <h4><span class="status-indicator status-unknown" id="statusModification"></span>Modification</h4>
                    <button class="btn-warning" onclick="testModification()">✏️ Tester Modification</button>
                    <div id="modifyResult" class="result"></div>
                </div>
            </div>
        </div>

        <!-- Interface Links -->
        <div class="test-section">
            <h3>🔗 Accès Interface Principale</h3>
            <div style="text-align: center;">
                <button class="btn-primary" onclick="window.open('/', '_blank')" style="margin: 10px; padding: 20px 30px; font-size: 18px;">
                    🖥️ Interface Principale
                </button>
                <button class="btn-success" onclick="window.open('/test-frontend-simple.html', '_blank')" style="margin: 10px; padding: 20px 30px; font-size: 18px;">
                    🧪 Test Frontend Simple
                </button>
            </div>
            <div class="warning result">
📋 INSTRUCTIONS FINALES:

1. Cliquez "LANCER TOUS LES TESTS" pour un diagnostic complet
2. Si tous les tests passent, l'interface est corrigée ✅
3. Ouvrez "Interface Principale" pour utiliser l'application
4. Vérifiez que les clients s'affichent et que l'ajout fonctionne

CORRECTIONS APPLIQUÉES:
✅ Erreur de syntaxe JavaScript corrigée
✅ Logs de debugging ajoutés
✅ Vérification DOM renforcée
✅ Fonctions dupliquées supprimées
✅ Gestion d'erreurs améliorée
            </div>
        </div>
    </div>

    <script>
        let testStats = { run: 0, success: 0, failed: 0 };
        let lastCreatedClientId = null;

        // Test complet
        async function runAllTests() {
            const btn = document.getElementById('runAllBtn');
            btn.disabled = true;
            btn.textContent = '🔄 Tests en cours...';
            
            testStats = { run: 0, success: 0, failed: 0 };
            updateTestStats();
            
            addResult('mainTestResult', 'info', '🚀 Démarrage de la suite de tests complète...\n');
            
            const tests = [
                { name: 'API Backend', func: testAPI, status: 'statusAPI' },
                { name: 'Interface Web', func: testInterface, status: 'statusInterface' },
                { name: 'Ajout Client', func: testAddClient, status: 'statusAjout' },
                { name: 'Modification', func: testModification, status: 'statusModification' }
            ];

            let allSuccess = true;

            for (let i = 0; i < tests.length; i++) {
                const test = tests[i];
                testStats.run++;
                updateTestStats();
                updateProgress((i / tests.length) * 100, `Test: ${test.name}...`);
                
                try {
                    addResult('mainTestResult', 'info', `🔄 ${test.name}...`);
                    await test.func();
                    testStats.success++;
                    updateStatus(test.status, 'ok');
                    addResult('mainTestResult', 'success', `✅ ${test.name}: RÉUSSI`);
                } catch (error) {
                    testStats.failed++;
                    allSuccess = false;
                    updateStatus(test.status, 'error');
                    addResult('mainTestResult', 'error', `❌ ${test.name}: ÉCHEC - ${error.message}`);
                }
                
                updateTestStats();
                await sleep(1000);
            }

            updateProgress(100, 'Tests terminés');
            
            const finalMessage = allSuccess ? 
                '🎉 TOUS LES TESTS RÉUSSIS! L\'interface est maintenant fonctionnelle!' :
                '⚠️ Certains tests ont échoué. Vérifiez les détails ci-dessus.';
                
            addResult('mainTestResult', allSuccess ? 'success' : 'warning', finalMessage);
            
            btn.disabled = false;
            btn.textContent = '🎯 LANCER TOUS LES TESTS';
        }

        // Tests individuels
        async function testAPI() {
            const response = await fetch('/api/clients');
            if (!response.ok) throw new Error(`HTTP ${response.status}`);
            
            const clients = await response.json();
            document.getElementById('totalClients').textContent = clients.length;
            
            addResult('apiResult', 'success', `✅ API fonctionnelle\n${clients.length} clients trouvés`);
        }

        async function testInterface() {
            // Test simplifié - vérifier que l'interface se charge
            const response = await fetch('/');
            if (!response.ok) throw new Error(`Interface non accessible: ${response.status}`);
            
            addResult('interfaceResult', 'success', '✅ Interface accessible\nOuvrez l\'interface principale pour vérifier l\'affichage');
        }

        async function testAddClient() {
            const clientData = {
                nom: 'TEST_FINAL',
                prenom: 'Client',
                email: `test.final.${Date.now()}@example.com`,
                telephone: '0123456789'
            };

            const response = await fetch('/api/clients', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json; charset=utf-8' },
                body: JSON.stringify(clientData)
            });

            if (!response.ok) {
                const error = await response.text();
                throw new Error(`Ajout échoué: ${error}`);
            }

            const result = await response.json();
            lastCreatedClientId = result.id;
            
            addResult('addResult', 'success', `✅ Client ajouté\nID: ${result.id}`);
        }

        async function testModification() {
            if (!lastCreatedClientId) {
                throw new Error('Aucun client à modifier. Exécutez d\'abord le test d\'ajout.');
            }

            const modificationData = {
                nom: 'TEST_MODIFIE',
                prenom: 'Client_Modifie',
                email: `test.modifie.${Date.now()}@example.com`
            };

            const response = await fetch(`/api/clients/${lastCreatedClientId}`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json; charset=utf-8' },
                body: JSON.stringify(modificationData)
            });

            if (!response.ok) {
                const error = await response.text();
                throw new Error(`Modification échouée: ${error}`);
            }

            addResult('modifyResult', 'success', `✅ Client modifié\nID: ${lastCreatedClientId}`);
        }

        // Utilitaires
        function updateProgress(percent, text = '') {
            document.getElementById('progressBar').style.width = `${percent}%`;
            document.getElementById('progressText').textContent = text || `${percent}%`;
        }

        function updateTestStats() {
            document.getElementById('testsRun').textContent = testStats.run;
            document.getElementById('testsSuccess').textContent = testStats.success;
            document.getElementById('testsFailed').textContent = testStats.failed;
        }

        function updateStatus(statusId, type) {
            const element = document.getElementById(statusId);
            element.className = `status-indicator status-${type}`;
        }

        function addResult(containerId, type, message) {
            const container = document.getElementById(containerId);
            const timestamp = new Date().toLocaleTimeString();
            const newMessage = `${timestamp} - ${message}`;
            
            if (container.textContent) {
                container.textContent += '\n' + newMessage;
            } else {
                container.textContent = newMessage;
            }
            
            container.className = `result ${type}`;
            container.scrollTop = container.scrollHeight;
        }

        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // Auto-start
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎯 Page de test final chargée');
            testAPI(); // Test automatique de l'API
        });
    </script>
</body>
</html>
