<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>✅ Test Interface Ultra Simple</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f0f0f0; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        .result { margin: 15px 0; padding: 20px; border-radius: 8px; font-family: monospace; white-space: pre-wrap; }
        .success { background: #d4edda; color: #155724; border: 2px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 2px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 2px solid #bee5eb; }
        button { padding: 15px 25px; margin: 10px; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; font-size: 16px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        h1 { color: #333; text-align: center; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #f9f9f9; }
        .client-list { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .client-item { padding: 10px; margin: 5px 0; background: white; border-radius: 5px; border: 1px solid #ddd; }
    </style>
</head>
<body>
    <div class="container">
        <h1>✅ Test Interface Ultra Simple</h1>
        
        <div class="test-section">
            <h3>🎯 RÉSUMÉ DU PROBLÈME</h3>
            <div class="info result">
L'API backend fonctionne parfaitement (3 clients existent).
Le problème est dans l'interface web qui n'affiche pas les clients.

Tests effectués:
✅ API GET /api/clients → 3 clients trouvés
✅ API POST /api/clients → Ajout fonctionne
❌ Interface web → N'affiche rien
            </div>
        </div>

        <div class="test-section">
            <h3>1. Vérification API</h3>
            <button class="btn-primary" onclick="verifyAPI()">🔍 Vérifier API</button>
            <div id="apiResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. Affichage Manuel des Clients</h3>
            <button class="btn-success" onclick="displayClientsManually()">📋 Afficher Clients</button>
            <div id="clientsDisplay" class="client-list"></div>
            <div id="displayResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. Test Ajout Simple</h3>
            <button class="btn-success" onclick="addTestClient()">➕ Ajouter Client Test</button>
            <div id="addResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. Interface Principale</h3>
            <button class="btn-primary" onclick="window.open('/', '_blank')" style="padding: 20px 30px; font-size: 18px;">
                🖥️ Ouvrir Interface Principale
            </button>
            <p><strong>Instructions:</strong> Ouvrez l'interface principale et vérifiez si les clients s'affichent maintenant.</p>
        </div>
    </div>

    <script>
        let allClients = [];

        // Vérifier l'API
        async function verifyAPI() {
            try {
                addResult('apiResult', 'info', 'Vérification de l\'API...');
                
                const response = await fetch('/api/clients');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                allClients = await response.json();
                
                addResult('apiResult', 'success', `✅ API FONCTIONNELLE
Statut: ${response.status}
Nombre de clients: ${allClients.length}

Clients trouvés:
${allClients.map((c, i) => `${i+1}. ${c.nom} ${c.prenom} (${c.email}) - Code: ${c.code_client || 'N/A'}`).join('\n')}`);
                
            } catch (error) {
                addResult('apiResult', 'error', `❌ Erreur API: ${error.message}`);
            }
        }

        // Afficher les clients manuellement
        async function displayClientsManually() {
            try {
                if (allClients.length === 0) {
                    await verifyAPI();
                }

                const clientsContainer = document.getElementById('clientsDisplay');
                
                if (allClients.length === 0) {
                    clientsContainer.innerHTML = `
                        <div class="client-item">
                            <h4>Aucun client trouvé</h4>
                            <p>La base de données est vide.</p>
                        </div>
                    `;
                    addResult('displayResult', 'info', 'Aucun client à afficher');
                    return;
                }

                clientsContainer.innerHTML = allClients.map(client => `
                    <div class="client-item">
                        <h4>${client.nom} ${client.prenom}</h4>
                        <p><strong>Email:</strong> ${client.email}</p>
                        <p><strong>Code:</strong> ${client.code_client || 'Non généré'}</p>
                        <p><strong>Téléphone:</strong> ${client.telephone || 'Non renseigné'}</p>
                        <p><strong>Ville:</strong> ${client.ville || 'Non renseignée'}</p>
                        <p><strong>Créé:</strong> ${new Date(client.date_creation).toLocaleDateString()}</p>
                    </div>
                `).join('');

                addResult('displayResult', 'success', `✅ ${allClients.length} clients affichés manuellement
L'affichage fonctionne, le problème est dans l'interface principale.`);
                
            } catch (error) {
                addResult('displayResult', 'error', `❌ Erreur affichage: ${error.message}`);
            }
        }

        // Ajouter un client test
        async function addTestClient() {
            try {
                addResult('addResult', 'info', 'Ajout d\'un client test...');
                
                const clientData = {
                    nom: 'ULTRA_TEST',
                    prenom: 'Client',
                    email: `ultra.test.${Date.now()}@example.com`,
                    telephone: '0123456789',
                    ville: 'Test City'
                };

                const response = await fetch('/api/clients', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json; charset=utf-8'
                    },
                    body: JSON.stringify(clientData)
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }

                const result = await response.json();
                
                addResult('addResult', 'success', `✅ CLIENT AJOUTÉ AVEC SUCCÈS
ID: ${result.id}
Message: ${result.message}

Maintenant, actualisez l'interface principale pour voir si le nouveau client apparaît.`);

                // Recharger la liste
                allClients = [];
                await displayClientsManually();
                
            } catch (error) {
                addResult('addResult', 'error', `❌ Erreur ajout: ${error.message}`);
            }
        }

        // Utilitaire
        function addResult(containerId, type, message) {
            const container = document.getElementById(containerId);
            container.className = `result ${type}`;
            container.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
        }

        // Auto-start
        document.addEventListener('DOMContentLoaded', () => {
            console.log('✅ Page de test ultra simple chargée');
            verifyAPI(); // Test automatique
        });
    </script>
</body>
</html>
