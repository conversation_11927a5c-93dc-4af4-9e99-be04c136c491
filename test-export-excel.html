<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 Test Export Excel - Clients</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        .result { margin: 15px 0; padding: 20px; border-radius: 8px; font-family: monospace; white-space: pre-wrap; }
        .success { background: #d4edda; color: #155724; border: 2px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 2px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 2px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 2px solid #ffeaa7; }
        button { padding: 15px 25px; margin: 10px; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; font-size: 16px; transition: all 0.3s; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-excel { background: #217346; color: white; }
        button:hover { transform: translateY(-2px); box-shadow: 0 4px 15px rgba(0,0,0,0.2); }
        h1 { color: #333; text-align: center; margin-bottom: 30px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #f9f9f9; }
        .test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
        .test-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .step { background: #e9ecef; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }
        .step-number { background: #007bff; color: white; border-radius: 50%; width: 30px; height: 30px; display: inline-flex; align-items: center; justify-content: center; margin-right: 10px; font-weight: bold; }
        .feature-highlight { background: linear-gradient(45deg, #28a745, #20c997); color: white; padding: 20px; border-radius: 10px; margin: 15px 0; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .stat-card { background: white; padding: 15px; border-radius: 8px; text-align: center; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .stat-number { font-size: 2em; font-weight: bold; color: #007bff; }
        .stat-label { color: #666; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 TEST EXPORT EXCEL - FONCTIONNALITÉ IMPLÉMENTÉE</h1>
        
        <div class="feature-highlight">
            <h2>🎉 NOUVELLE FONCTIONNALITÉ : EXPORT EXCEL</h2>
            <p><strong>✅ Implémentée avec succès !</strong> Vous pouvez maintenant exporter vos clients au format Excel (.xlsx) avec tous les champs et une mise en forme professionnelle.</p>
        </div>

        <div class="test-section">
            <h3>📋 Fonctionnalités d'Export Disponibles</h3>
            <div class="test-grid">
                <div class="test-card">
                    <h4>📊 Export Excel (.xlsx)</h4>
                    <div class="success result">
✅ NOUVEAU : Format Excel professionnel
✅ Tous les champs clients (33 colonnes)
✅ Largeurs de colonnes optimisées
✅ Compression du fichier
✅ Nom de fichier avec date
✅ Headers français complets
                    </div>
                    <button class="btn-excel" onclick="testExportExcel()">
                        <i class="fas fa-file-excel"></i> Tester Export Excel
                    </button>
                    <div id="excelResult" class="result"></div>
                </div>
                
                <div class="test-card">
                    <h4>📄 Export CSV (.csv)</h4>
                    <div class="info result">
✅ Format CSV classique
✅ Compatible avec tous les tableurs
✅ Encodage UTF-8
✅ Séparateurs optimisés
                    </div>
                    <button class="btn-warning" onclick="testExportCSV()">
                        <i class="fas fa-file-csv"></i> Tester Export CSV
                    </button>
                    <div id="csvResult" class="result"></div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 Détails Techniques de l'Export Excel</h3>
            <div class="info result">
📋 COLONNES EXPORTÉES (33 au total):

1. Code Client          18. Pays de Naissance ID
2. Nom                  19. Lieu de Naissance ID  
3. Prénom               20. Compte Comptable ID
4. Email                21. Label Tél ID
5. Téléphone            22. Label Mobile 1 ID
6. Mobile 1             23. Label Mobile 2 ID
7. Mobile 2             24. Nom Arabe
8. Adresse              25. Prénom Arabe
9. Ville                26. N° Carte Fidélité
10. Code Postal         27. Compagnie Aérienne ID
11. CIN N°              28. Nom Personne Liée
12. Passeport N°        29. Label Personne Liée ID
13. Date Expiration     30. Tél Personne Liée
14. Date de Naissance   31. Photo URL
15. Nationalité ID      32. Date Création
16. Sexe ID             33. Date Modification
17. Situation Familiale ID

🔧 OPTIMISATIONS:
- Largeurs de colonnes ajustées automatiquement
- Compression du fichier pour réduire la taille
- Format .xlsx moderne (Excel 2007+)
- Encodage UTF-8 pour les caractères arabes
- Headers en français pour une meilleure lisibilité
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 Tests Automatiques</h3>
            <div class="test-grid">
                <div class="test-card">
                    <h4>🔍 Test API Export</h4>
                    <button class="btn-primary" onclick="testExportAPI()">Tester API</button>
                    <div id="apiResult" class="result"></div>
                </div>
                
                <div class="test-card">
                    <h4>📊 Statistiques Clients</h4>
                    <button class="btn-success" onclick="loadClientStats()">Charger Stats</button>
                    <div id="statsContainer" class="stats"></div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🚀 Interface Principale</h3>
            <div class="step">
                <span class="step-number">1</span>
                <strong>Accéder à l'interface</strong>
                <button class="btn-primary" onclick="window.open('/', '_blank')">
                    🖥️ Ouvrir Interface Principale
                </button>
            </div>

            <div class="step">
                <span class="step-number">2</span>
                <strong>Utiliser l'export</strong>
                <div class="warning result">
📋 PROCÉDURE D'EXPORT:

1. Cliquez sur le bouton "Exporter" dans l'interface principale
2. Choisissez le format : Excel (.xlsx) ou CSV (.csv)
3. Le fichier se télécharge automatiquement
4. Ouvrez le fichier dans Excel, LibreOffice, ou Google Sheets

💡 CONSEIL: Choisissez Excel pour une meilleure présentation !
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>📈 Avantages de l'Export Excel</h3>
            <div class="success result">
🎯 POURQUOI CHOISIR EXCEL ?

✅ Meilleure lisibilité avec colonnes ajustées
✅ Format professionnel reconnu partout
✅ Compatible avec Microsoft Excel, LibreOffice, Google Sheets
✅ Fichiers plus compacts (compression intégrée)
✅ Support natif des caractères arabes
✅ Possibilité d'ajouter des formules et graphiques
✅ Impression optimisée avec mise en page automatique

📊 UTILISATION RECOMMANDÉE:
- Rapports clients pour la direction
- Analyses statistiques
- Archivage des données
- Partage avec des partenaires
- Import dans d'autres systèmes
            </div>
        </div>
    </div>

    <script>
        // Test export Excel direct
        async function testExportExcel() {
            const result = document.getElementById('excelResult');
            result.className = 'result info';
            result.textContent = 'Test export Excel en cours...';
            
            try {
                const response = await fetch('/api/clients/export/excel');
                if (!response.ok) throw new Error('Erreur API: ' + response.status);
                
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                
                link.href = url;
                link.download = `test_clients_export_${new Date().toISOString().split('T')[0]}.xlsx`;
                link.click();
                
                window.URL.revokeObjectURL(url);
                
                result.className = 'result success';
                result.textContent = `✅ Export Excel réussi !
                
Fichier téléchargé: test_clients_export_${new Date().toISOString().split('T')[0]}.xlsx
Taille: ${(blob.size / 1024).toFixed(2)} KB
Format: Excel (.xlsx)

Le fichier a été téléchargé dans votre dossier de téléchargements.
Ouvrez-le avec Excel, LibreOffice ou Google Sheets pour voir le résultat !`;
                
            } catch (error) {
                result.className = 'result error';
                result.textContent = `❌ Erreur: ${error.message}`;
            }
        }

        // Test export CSV
        async function testExportCSV() {
            const result = document.getElementById('csvResult');
            result.className = 'result info';
            result.textContent = 'Test export CSV en cours...';
            
            try {
                const response = await fetch('/api/clients');
                const clients = await response.json();
                
                result.className = 'result success';
                result.textContent = `✅ Export CSV disponible !
                
Nombre de clients: ${clients.length}
Format: CSV (.csv)
Encodage: UTF-8

Utilisez le bouton "Exporter" dans l'interface principale pour télécharger.`;
                
            } catch (error) {
                result.className = 'result error';
                result.textContent = `❌ Erreur: ${error.message}`;
            }
        }

        // Test API
        async function testExportAPI() {
            const result = document.getElementById('apiResult');
            result.className = 'result info';
            result.textContent = 'Test API en cours...';
            
            try {
                const response = await fetch('/api/clients/export/excel', { method: 'HEAD' });
                
                result.className = 'result success';
                result.textContent = `✅ API Export Excel opérationnelle !
                
Endpoint: /api/clients/export/excel
Status: ${response.status} ${response.statusText}
Content-Type: ${response.headers.get('content-type') || 'N/A'}

L'API est prête à générer des fichiers Excel !`;
                
            } catch (error) {
                result.className = 'result error';
                result.textContent = `❌ Erreur API: ${error.message}`;
            }
        }

        // Charger les statistiques
        async function loadClientStats() {
            const container = document.getElementById('statsContainer');
            container.innerHTML = '<div class="info result">Chargement des statistiques...</div>';
            
            try {
                const response = await fetch('/api/clients');
                const clients = await response.json();
                
                container.innerHTML = `
                    <div class="stat-card">
                        <div class="stat-number">${clients.length}</div>
                        <div class="stat-label">Total Clients</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">33</div>
                        <div class="stat-label">Colonnes Excel</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">2</div>
                        <div class="stat-label">Formats Export</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">✅</div>
                        <div class="stat-label">Statut API</div>
                    </div>
                `;
                
            } catch (error) {
                container.innerHTML = `<div class="error result">❌ Erreur: ${error.message}</div>`;
            }
        }

        // Auto-load au démarrage
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🧪 Page de test export Excel chargée');
            loadClientStats();
        });
    </script>
</body>
</html>
