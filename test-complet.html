<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Test Complet Interface</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .test-container { max-width: 1200px; margin: 0 auto; }
        .test-section { 
            background: white; 
            margin: 20px 0; 
            padding: 20px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section h3 { 
            color: #333; 
            border-bottom: 2px solid #007bff; 
            padding-bottom: 10px; 
        }
        .test-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); 
            gap: 20px; 
        }
        .test-card { 
            border: 1px solid #ddd; 
            padding: 15px; 
            border-radius: 8px; 
            background: #f9f9f9;
        }
        button { 
            padding: 10px 20px; 
            margin: 5px; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
            font-weight: bold;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        .result { 
            margin: 10px 0; 
            padding: 10px; 
            border-radius: 5px; 
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .stats { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); 
            gap: 15px; 
            margin: 15px 0;
        }
        .stat-card { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            padding: 20px; 
            border-radius: 10px; 
            text-align: center;
        }
        .stat-number { font-size: 2rem; font-weight: bold; }
        .stat-label { font-size: 0.9rem; opacity: 0.9; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Test Complet - Interface Gestion Clients</h1>
        
        <!-- Section Statistiques -->
        <div class="test-section">
            <h3>📊 Statistiques en Temps Réel</h3>
            <button class="btn-primary" onclick="loadStats()">🔄 Actualiser Statistiques</button>
            <div id="statsContainer" class="stats">
                <div class="stat-card">
                    <div class="stat-number" id="totalClients">-</div>
                    <div class="stat-label">Total Clients</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="thisMonth">-</div>
                    <div class="stat-label">Ce Mois</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="thisYear">-</div>
                    <div class="stat-label">Cette Année</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="averageAge">-</div>
                    <div class="stat-label">Âge Moyen</div>
                </div>
            </div>
            <div id="statsResult" class="result"></div>
        </div>

        <!-- Section Tests API -->
        <div class="test-section">
            <h3>🔌 Tests API</h3>
            <div class="test-grid">
                <div class="test-card">
                    <h4>📋 Clients</h4>
                    <button class="btn-primary" onclick="testClients()">Tester GET /api/clients</button>
                    <div id="clientsResult" class="result"></div>
                </div>
                <div class="test-card">
                    <h4>📚 Références</h4>
                    <button class="btn-primary" onclick="testReferences()">Tester GET /api/references/all</button>
                    <div id="referencesResult" class="result"></div>
                </div>
                <div class="test-card">
                    <h4>📊 Statistiques</h4>
                    <button class="btn-primary" onclick="testStatsAPI()">Tester GET /api/clients/stats</button>
                    <div id="statsAPIResult" class="result"></div>
                </div>
            </div>
        </div>

        <!-- Section Tests CRUD -->
        <div class="test-section">
            <h3>✏️ Tests CRUD</h3>
            <div class="test-grid">
                <div class="test-card">
                    <h4>➕ Créer Client</h4>
                    <button class="btn-success" onclick="testCreateClient()">Créer Client Test</button>
                    <div id="createResult" class="result"></div>
                </div>
                <div class="test-card">
                    <h4>📝 Modifier Client</h4>
                    <button class="btn-warning" onclick="testUpdateClient()">Modifier Dernier Client</button>
                    <div id="updateResult" class="result"></div>
                </div>
                <div class="test-card">
                    <h4>🗑️ Supprimer Client</h4>
                    <button class="btn-danger" onclick="testDeleteClient()">Supprimer Client Test</button>
                    <div id="deleteResult" class="result"></div>
                </div>
            </div>
        </div>

        <!-- Section Import/Export -->
        <div class="test-section">
            <h3>📤📥 Tests Import/Export</h3>
            <div class="test-grid">
                <div class="test-card">
                    <h4>📤 Export</h4>
                    <button class="btn-success" onclick="testExport()">Exporter Clients CSV</button>
                    <div id="exportResult" class="result"></div>
                </div>
                <div class="test-card">
                    <h4>📥 Import</h4>
                    <input type="file" id="importFile" accept=".csv,.json" style="margin: 10px 0;">
                    <button class="btn-warning" onclick="testImport()">Importer Fichier</button>
                    <div id="importResult" class="result"></div>
                </div>
                <div class="test-card">
                    <h4>📄 Générer CSV Test</h4>
                    <button class="btn-primary" onclick="generateTestCSV()">Créer CSV Test</button>
                    <div id="csvResult" class="result"></div>
                </div>
            </div>
        </div>

        <!-- Section Interface -->
        <div class="test-section">
            <h3>🖥️ Interface Principale</h3>
            <div style="text-align: center;">
                <button class="btn-primary" onclick="openMainInterface()" style="font-size: 1.2rem; padding: 15px 30px;">
                    🚀 Ouvrir Interface Principale
                </button>
                <p style="margin-top: 15px; color: #666;">
                    Cliquez pour ouvrir l'interface principale dans un nouvel onglet
                </p>
            </div>
        </div>
    </div>

    <script>
        let lastClientId = null;

        // Charger les statistiques
        async function loadStats() {
            try {
                const response = await fetch('/api/clients/stats');
                const stats = await response.json();
                
                document.getElementById('totalClients').textContent = stats.total;
                document.getElementById('thisMonth').textContent = stats.thisMonth;
                document.getElementById('thisYear').textContent = stats.thisYear;
                document.getElementById('averageAge').textContent = stats.averageAge || '-';
                
                document.getElementById('statsResult').innerHTML = 
                    `<div class="success">✅ Statistiques chargées avec succès</div>`;
            } catch (error) {
                document.getElementById('statsResult').innerHTML = 
                    `<div class="error">❌ Erreur: ${error.message}</div>`;
            }
        }

        // Test API clients
        async function testClients() {
            try {
                const response = await fetch('/api/clients');
                const clients = await response.json();
                document.getElementById('clientsResult').innerHTML = 
                    `<div class="success">✅ ${clients.length} clients récupérés</div>`;
            } catch (error) {
                document.getElementById('clientsResult').innerHTML = 
                    `<div class="error">❌ Erreur: ${error.message}</div>`;
            }
        }

        // Test API références
        async function testReferences() {
            try {
                const response = await fetch('/api/references/all');
                const refs = await response.json();
                const count = Object.keys(refs).reduce((sum, key) => sum + (refs[key]?.length || 0), 0);
                document.getElementById('referencesResult').innerHTML = 
                    `<div class="success">✅ ${count} références chargées</div>`;
            } catch (error) {
                document.getElementById('referencesResult').innerHTML = 
                    `<div class="error">❌ Erreur: ${error.message}</div>`;
            }
        }

        // Test API statistiques
        async function testStatsAPI() {
            try {
                const response = await fetch('/api/clients/stats');
                const stats = await response.json();
                document.getElementById('statsAPIResult').innerHTML = 
                    `<div class="success">✅ Stats API: ${JSON.stringify(stats, null, 2)}</div>`;
            } catch (error) {
                document.getElementById('statsAPIResult').innerHTML = 
                    `<div class="error">❌ Erreur: ${error.message}</div>`;
            }
        }

        // Test création client
        async function testCreateClient() {
            const clientData = {
                nom: 'TEST',
                prenom: 'Client',
                email: `test.${Date.now()}@example.com`,
                telephone: '0123456789',
                ville: 'Test City'
            };

            try {
                const response = await fetch('/api/clients', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(clientData)
                });

                if (!response.ok) throw new Error(await response.text());

                const result = await response.json();
                lastClientId = result.id;
                document.getElementById('createResult').innerHTML = 
                    `<div class="success">✅ Client créé avec ID: ${result.id}</div>`;
                loadStats(); // Actualiser les stats
            } catch (error) {
                document.getElementById('createResult').innerHTML = 
                    `<div class="error">❌ Erreur: ${error.message}</div>`;
            }
        }

        // Test modification client
        async function testUpdateClient() {
            if (!lastClientId) {
                document.getElementById('updateResult').innerHTML = 
                    `<div class="error">❌ Aucun client à modifier. Créez d'abord un client.</div>`;
                return;
            }

            const updateData = {
                nom: 'TEST_MODIFIE',
                prenom: 'Client_Modifie',
                ville: 'Modified City'
            };

            try {
                const response = await fetch(`/api/clients/${lastClientId}`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(updateData)
                });

                if (!response.ok) throw new Error(await response.text());

                document.getElementById('updateResult').innerHTML = 
                    `<div class="success">✅ Client ${lastClientId} modifié avec succès</div>`;
            } catch (error) {
                document.getElementById('updateResult').innerHTML = 
                    `<div class="error">❌ Erreur: ${error.message}</div>`;
            }
        }

        // Test suppression client
        async function testDeleteClient() {
            if (!lastClientId) {
                document.getElementById('deleteResult').innerHTML = 
                    `<div class="error">❌ Aucun client à supprimer. Créez d'abord un client.</div>`;
                return;
            }

            try {
                const response = await fetch(`/api/clients/${lastClientId}`, {
                    method: 'DELETE'
                });

                if (!response.ok) throw new Error(await response.text());

                document.getElementById('deleteResult').innerHTML = 
                    `<div class="success">✅ Client ${lastClientId} supprimé avec succès</div>`;
                lastClientId = null;
                loadStats(); // Actualiser les stats
            } catch (error) {
                document.getElementById('deleteResult').innerHTML = 
                    `<div class="error">❌ Erreur: ${error.message}</div>`;
            }
        }

        // Test export
        async function testExport() {
            try {
                const response = await fetch('/api/clients');
                const clients = await response.json();
                
                // Simuler l'export (dans la vraie interface, cela téléchargerait le fichier)
                document.getElementById('exportResult').innerHTML = 
                    `<div class="success">✅ Export simulé: ${clients.length} clients prêts à être exportés</div>`;
            } catch (error) {
                document.getElementById('exportResult').innerHTML = 
                    `<div class="error">❌ Erreur: ${error.message}</div>`;
            }
        }

        // Test import
        function testImport() {
            const file = document.getElementById('importFile').files[0];
            if (!file) {
                document.getElementById('importResult').innerHTML = 
                    `<div class="error">❌ Veuillez sélectionner un fichier</div>`;
                return;
            }

            document.getElementById('importResult').innerHTML = 
                `<div class="info">ℹ️ Import simulé pour: ${file.name}</div>`;
        }

        // Générer un CSV de test
        function generateTestCSV() {
            const csvContent = `Code Client,Nom,Prénom,Email,Téléphone,Mobile 1,Mobile 2,Adresse,Ville,Code Postal,CIN,Passeport,Date Naissance,Nationalité,Sexe,Situation Familiale,Nom Arabe,Prénom Arabe,Carte Fidélité,Contact Urgence,Tel Urgence,Date Création
CLI000001,"DUPONT","Jean","<EMAIL>","0123456789","0612345678","","123 Rue Test","Paris","75001","AB123456","M1234567","1985-05-15","Française","Masculin","Marié","","","","Marie Dupont","0687654321","2025-01-01"
CLI000002,"MARTIN","Sophie","<EMAIL>","0234567890","0623456789","","456 Avenue Test","Lyon","69001","CD789012","M7890123","1990-08-22","Française","Féminin","Célibataire","","","RAM123456","","","2025-01-02"`;

            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            
            link.setAttribute('href', url);
            link.setAttribute('download', 'clients_test.csv');
            link.style.visibility = 'hidden';
            
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            document.getElementById('csvResult').innerHTML = 
                `<div class="success">✅ Fichier CSV de test généré et téléchargé</div>`;
        }

        // Ouvrir l'interface principale
        function openMainInterface() {
            window.open('/', '_blank');
        }

        // Charger les stats au démarrage
        document.addEventListener('DOMContentLoaded', () => {
            loadStats();
        });
    </script>
</body>
</html>
