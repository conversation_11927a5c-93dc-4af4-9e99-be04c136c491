<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚨 Test Ajout Urgent</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f0f0f0; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        .result { margin: 15px 0; padding: 20px; border-radius: 8px; font-family: monospace; white-space: pre-wrap; }
        .success { background: #d4edda; color: #155724; border: 2px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 2px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 2px solid #bee5eb; }
        button { padding: 15px 25px; margin: 10px; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; font-size: 16px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        h1 { color: #333; text-align: center; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #f9f9f9; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚨 Test Ajout Client URGENT</h1>
        
        <div class="test-section">
            <h3>1. Test API Directe</h3>
            <button class="btn-primary" onclick="testAPI()">🔍 Tester API /clients</button>
            <div id="apiResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. Test Ajout Simple</h3>
            <button class="btn-success" onclick="testAddSimple()">➕ Ajouter Client Simple</button>
            <div id="addResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. Test Ajout Complet</h3>
            <button class="btn-success" onclick="testAddComplete()">➕ Ajouter Client Complet</button>
            <div id="completeResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. Vérification</h3>
            <button class="btn-primary" onclick="verifyClients()">📋 Vérifier Clients</button>
            <div id="verifyResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🔗 Interface Principale</h3>
            <button class="btn-primary" onclick="window.open('/', '_blank')" style="padding: 20px 30px; font-size: 18px;">
                🖥️ Ouvrir Interface Principale
            </button>
        </div>
    </div>

    <script>
        // Test API de base
        async function testAPI() {
            try {
                addResult('apiResult', 'info', 'Test de l\'API...');
                
                const response = await fetch('/api/clients');
                console.log('API Response:', response.status, response.statusText);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const clients = await response.json();
                console.log('Clients récupérés:', clients);
                
                addResult('apiResult', 'success', `✅ API fonctionnelle
Statut: ${response.status}
Nombre de clients: ${clients.length}
${clients.length > 0 ? 'Clients existants trouvés' : 'Aucun client (normal si première utilisation)'}`);
                
            } catch (error) {
                console.error('Erreur API:', error);
                addResult('apiResult', 'error', `❌ Erreur API: ${error.message}`);
            }
        }

        // Test ajout simple
        async function testAddSimple() {
            try {
                addResult('addResult', 'info', 'Test ajout client simple...');
                
                const clientData = {
                    nom: 'TEST',
                    prenom: 'Simple',
                    email: `test.simple.${Date.now()}@example.com`
                };

                console.log('Données à envoyer:', clientData);
                
                const response = await fetch('/api/clients', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json; charset=utf-8'
                    },
                    body: JSON.stringify(clientData)
                });

                console.log('Response:', response.status, response.statusText);
                
                const responseText = await response.text();
                console.log('Response text:', responseText);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${responseText}`);
                }

                let result;
                try {
                    result = JSON.parse(responseText);
                } catch {
                    result = { message: responseText };
                }

                console.log('Résultat:', result);
                
                addResult('addResult', 'success', `✅ Client simple ajouté avec succès!
ID: ${result.id || 'Non spécifié'}
Réponse: ${JSON.stringify(result, null, 2)}`);
                
            } catch (error) {
                console.error('Erreur ajout simple:', error);
                addResult('addResult', 'error', `❌ Erreur ajout simple: ${error.message}`);
            }
        }

        // Test ajout complet
        async function testAddComplete() {
            try {
                addResult('completeResult', 'info', 'Test ajout client complet...');
                
                const clientData = {
                    nom: 'HABIBI',
                    prenom: 'Karim',
                    email: `karim.habibi.${Date.now()}@example.com`,
                    telephone: '0661452223',
                    mobile_1: '0612345678',
                    adresse: '123 Rue Test',
                    ville: 'Casablanca',
                    code_postal: '20000',
                    cin_no: 'AB123456',
                    passeport_no: 'M1234567',
                    nationality_id: 1,
                    sexe_id: 1,
                    nom_arabe: 'حبيبي',
                    prenom_arabe: 'كريم'
                };

                console.log('Données complètes à envoyer:', clientData);
                
                const response = await fetch('/api/clients', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json; charset=utf-8'
                    },
                    body: JSON.stringify(clientData)
                });

                console.log('Response:', response.status, response.statusText);
                
                const responseText = await response.text();
                console.log('Response text:', responseText);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${responseText}`);
                }

                let result;
                try {
                    result = JSON.parse(responseText);
                } catch {
                    result = { message: responseText };
                }

                console.log('Résultat complet:', result);
                
                addResult('completeResult', 'success', `✅ Client complet ajouté avec succès!
ID: ${result.id || 'Non spécifié'}
Avec tous les nouveaux champs
Réponse: ${JSON.stringify(result, null, 2)}`);
                
            } catch (error) {
                console.error('Erreur ajout complet:', error);
                addResult('completeResult', 'error', `❌ Erreur ajout complet: ${error.message}`);
            }
        }

        // Vérifier les clients
        async function verifyClients() {
            try {
                addResult('verifyResult', 'info', 'Vérification des clients...');
                
                const response = await fetch('/api/clients');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const clients = await response.json();
                console.log('Clients vérifiés:', clients);
                
                let details = `📊 Résultats de vérification:
Nombre total de clients: ${clients.length}

`;

                if (clients.length > 0) {
                    details += 'Liste des clients:\n';
                    clients.forEach((client, index) => {
                        details += `${index + 1}. ID: ${client.id} - ${client.nom} ${client.prenom}
   Email: ${client.email}
   Code: ${client.code_client || 'Non généré'}
   Ville: ${client.ville || 'Non spécifiée'}
   Créé: ${client.date_creation}

`;
                    });
                } else {
                    details += 'Aucun client trouvé dans la base de données.';
                }
                
                addResult('verifyResult', clients.length > 0 ? 'success' : 'info', details);
                
            } catch (error) {
                console.error('Erreur vérification:', error);
                addResult('verifyResult', 'error', `❌ Erreur vérification: ${error.message}`);
            }
        }

        // Utilitaire
        function addResult(containerId, type, message) {
            const container = document.getElementById(containerId);
            container.className = `result ${type}`;
            container.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
        }

        // Auto-start
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚨 Page de test urgent chargée');
            testAPI(); // Test automatique de l'API au chargement
        });
    </script>
</body>
</html>
