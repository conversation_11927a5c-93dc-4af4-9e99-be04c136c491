/* Styles pour le modal moderne */

/* Modal de base */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modern-modal {
    background: var(--white);
    margin: 2% auto;
    border-radius: var(--radius-xl);
    width: 95%;
    max-width: 1000px;
    max-height: 95vh;
    overflow: hidden;
    box-shadow: var(--shadow-xl);
    animation: slideIn 0.3s ease-out;
    display: flex;
    flex-direction: column;
}

@keyframes slideIn {
    from { 
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to { 
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Header du modal */
.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: var(--white);
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.modal-title-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.modal-title-section i {
    font-size: 1.5rem;
}

.modal-title-section h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.header-controls {
    display: flex;
    gap: 0.5rem;
}

/* Système d'onglets */
.tabs-container {
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.tabs-nav {
    display: flex;
    padding: 0 2rem;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.tabs-nav::-webkit-scrollbar {
    display: none;
}

.tab-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 1.5rem;
    border: none;
    background: none;
    color: var(--gray-600);
    font-weight: 500;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: var(--transition);
    white-space: nowrap;
}

.tab-btn:hover {
    color: var(--primary-color);
    background: var(--white);
}

.tab-btn.active {
    color: var(--primary-color);
    background: var(--white);
    border-bottom-color: var(--primary-color);
}

.tab-btn i {
    font-size: 1rem;
}

/* Contenu des onglets */
.tab-content {
    display: none;
    padding: 2rem;
    overflow-y: auto;
    max-height: 60vh;
}

.tab-content.active {
    display: block;
}

.tab-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--gray-200);
}

.tab-header h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.tab-header p {
    color: var(--gray-600);
    font-size: 0.875rem;
}

/* Grille de formulaire */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.875rem;
}

.form-group label i {
    color: var(--primary-color);
    width: 1rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 0.75rem 1rem;
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    transition: var(--transition);
    background: var(--white);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-group input[type="date"] {
    color: var(--gray-700);
}

/* Section photo moderne */
.photo-section-modern {
    display: flex;
    justify-content: center;
    margin-bottom: 2rem;
}

.photo-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.photo-preview {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 4px solid var(--primary-color);
    overflow: hidden;
    position: relative;
    background: var(--gray-100);
    display: flex;
    align-items: center;
    justify-content: center;
}

.photo-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.photo-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    color: var(--gray-400);
    text-align: center;
}

.photo-placeholder i {
    font-size: 2rem;
}

.photo-placeholder span {
    font-size: 0.75rem;
    font-weight: 500;
}

.photo-actions {
    display: flex;
    gap: 0.5rem;
}

/* Sections spécialisées */
.contact-section,
.travel-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-200);
}

.contact-section h5,
.travel-section h5 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.contact-section h5 i,
.travel-section h5 i {
    color: var(--primary-color);
}

/* Carte de fidélité preview */
.loyalty-preview {
    margin-top: 1rem;
}

.loyalty-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    color: var(--white);
    box-shadow: var(--shadow-lg);
    max-width: 350px;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.airline-name {
    font-weight: 700;
    font-size: 1.125rem;
}

.card-type {
    font-size: 0.75rem;
    opacity: 0.8;
}

.card-number {
    font-family: 'Courier New', monospace;
    font-size: 1.25rem;
    font-weight: 700;
    letter-spacing: 2px;
    margin-bottom: 0.5rem;
}

.card-holder {
    font-size: 0.875rem;
    opacity: 0.9;
}

/* Actions du modal */
.modal-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    background: var(--gray-50);
    border-top: 1px solid var(--gray-200);
    border-radius: 0 0 var(--radius-xl) var(--radius-xl);
}

.actions-left,
.actions-right {
    display: flex;
    gap: 1rem;
}

/* Responsive pour modal */
@media (max-width: 768px) {
    .modern-modal {
        width: 98%;
        margin: 1% auto;
        max-height: 98vh;
    }
    
    .modal-header {
        padding: 1rem;
    }
    
    .tab-content {
        padding: 1rem;
        max-height: 70vh;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .modal-actions {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
    }
    
    .actions-left,
    .actions-right {
        width: 100%;
        justify-content: center;
    }
}
