<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Test Interface Corrigée</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f0f0f0; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        .result { margin: 15px 0; padding: 20px; border-radius: 8px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto; }
        .success { background: #d4edda; color: #155724; border: 2px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 2px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 2px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 2px solid #ffeaa7; }
        button { padding: 15px 25px; margin: 10px; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; font-size: 16px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        h1 { color: #333; text-align: center; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #f9f9f9; }
        .status-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .status-card { padding: 15px; border-radius: 8px; text-align: center; font-weight: bold; }
        .status-ok { background: #d4edda; color: #155724; }
        .status-error { background: #f8d7da; color: #721c24; }
        .status-unknown { background: #e2e3e5; color: #6c757d; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Test Interface Corrigée</h1>
        
        <div class="test-section">
            <h3>📊 État des Corrections</h3>
            <div class="info result">
✅ Erreur de syntaxe corrigée (accolade en trop)
✅ Logs de debugging ajoutés
✅ Vérification DOM renforcée
✅ Fonction showError dédupliquée
            </div>
        </div>

        <div class="test-section">
            <h3>1. Test API Backend</h3>
            <button class="btn-primary" onclick="testAPI()">🔍 Tester API</button>
            <div id="apiResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. Test Interface Principale</h3>
            <div class="status-grid">
                <div class="status-card status-unknown" id="statusAPI">API: Test en cours...</div>
                <div class="status-card status-unknown" id="statusInterface">Interface: Non testée</div>
                <div class="status-card status-unknown" id="statusClients">Clients: Non vérifiés</div>
                <div class="status-card status-unknown" id="statusAjout">Ajout: Non testé</div>
            </div>
            
            <button class="btn-success" onclick="testInterface()">🖥️ Tester Interface</button>
            <button class="btn-primary" onclick="window.open('/', '_blank')" style="padding: 20px 30px; font-size: 18px;">
                🚀 Ouvrir Interface Principale
            </button>
            <div id="interfaceResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. Test Ajout Client</h3>
            <button class="btn-success" onclick="testAddClient()">➕ Ajouter Client Test</button>
            <div id="addResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. Instructions de Test Manuel</h3>
            <div class="warning result">
📋 INSTRUCTIONS POUR TESTER L'INTERFACE PRINCIPALE:

1. Cliquez "Ouvrir Interface Principale"
2. Ouvrez la console (F12)
3. Vérifiez les logs:
   - "🚀 Initialisation de l'application..."
   - "🔄 Chargement des clients..."
   - "📋 Clients récupérés: X clients"
   - "✅ Chargement terminé"

4. Si vous voyez des erreurs en rouge, notez-les
5. Vérifiez si les clients s'affichent maintenant
6. Testez "Nouveau Client" pour ajouter un client

RÉSULTAT ATTENDU:
✅ Les clients existants s'affichent
✅ Le bouton "Nouveau Client" fonctionne
✅ Aucune erreur dans la console
            </div>
        </div>
    </div>

    <script>
        let testResults = {
            api: false,
            interface: false,
            clients: false,
            ajout: false
        };

        // Test API
        async function testAPI() {
            try {
                addResult('apiResult', 'info', 'Test de l\'API...');
                updateStatus('statusAPI', 'Test en cours...', 'unknown');
                
                const response = await fetch('/api/clients');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const clients = await response.json();
                
                testResults.api = true;
                updateStatus('statusAPI', `API OK (${clients.length} clients)`, 'ok');
                updateStatus('statusClients', `${clients.length} clients trouvés`, 'ok');
                
                addResult('apiResult', 'success', `✅ API FONCTIONNELLE
Statut: ${response.status}
Nombre de clients: ${clients.length}
Clients: ${clients.map(c => `${c.nom} ${c.prenom}`).join(', ')}`);
                
            } catch (error) {
                testResults.api = false;
                updateStatus('statusAPI', 'API ERREUR', 'error');
                addResult('apiResult', 'error', `❌ Erreur API: ${error.message}`);
            }
        }

        // Test interface
        async function testInterface() {
            try {
                addResult('interfaceResult', 'info', 'Test de l\'interface...');
                updateStatus('statusInterface', 'Test en cours...', 'unknown');
                
                // Simuler l'ouverture de l'interface dans un iframe
                const iframe = document.createElement('iframe');
                iframe.src = '/';
                iframe.style.width = '1px';
                iframe.style.height = '1px';
                iframe.style.opacity = '0';
                iframe.style.position = 'absolute';
                iframe.style.top = '-1000px';
                document.body.appendChild(iframe);
                
                iframe.onload = () => {
                    try {
                        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        const clientsContainer = iframeDoc.getElementById('clientsContainer');
                        const addClientBtn = iframeDoc.getElementById('addClientBtn');
                        
                        let status = 'Interface OK';
                        let details = '✅ Interface chargée\n';
                        
                        if (clientsContainer) {
                            details += '✅ clientsContainer trouvé\n';
                        } else {
                            details += '❌ clientsContainer MANQUANT\n';
                            status = 'Interface ERREUR';
                        }
                        
                        if (addClientBtn) {
                            details += '✅ addClientBtn trouvé\n';
                        } else {
                            details += '❌ addClientBtn MANQUANT\n';
                            status = 'Interface ERREUR';
                        }
                        
                        // Vérifier le contenu
                        if (clientsContainer) {
                            const content = clientsContainer.innerHTML;
                            if (content.includes('client-card') || content.includes('no-clients')) {
                                details += '✅ Contenu affiché\n';
                            } else if (content.includes('Chargement')) {
                                details += '⏳ Chargement en cours\n';
                            } else {
                                details += '❌ Contenu vide ou erreur\n';
                            }
                        }
                        
                        testResults.interface = status === 'Interface OK';
                        updateStatus('statusInterface', status, testResults.interface ? 'ok' : 'error');
                        addResult('interfaceResult', testResults.interface ? 'success' : 'warning', details);
                        
                        document.body.removeChild(iframe);
                        
                    } catch (error) {
                        testResults.interface = false;
                        updateStatus('statusInterface', 'Interface ERREUR', 'error');
                        addResult('interfaceResult', 'error', `❌ Erreur test interface: ${error.message}`);
                        document.body.removeChild(iframe);
                    }
                };
                
                iframe.onerror = () => {
                    testResults.interface = false;
                    updateStatus('statusInterface', 'Interface ERREUR', 'error');
                    addResult('interfaceResult', 'error', '❌ Erreur chargement interface');
                    document.body.removeChild(iframe);
                };
                
            } catch (error) {
                testResults.interface = false;
                updateStatus('statusInterface', 'Interface ERREUR', 'error');
                addResult('interfaceResult', 'error', `❌ Erreur: ${error.message}`);
            }
        }

        // Test ajout client
        async function testAddClient() {
            try {
                addResult('addResult', 'info', 'Test d\'ajout de client...');
                updateStatus('statusAjout', 'Test en cours...', 'unknown');
                
                const clientData = {
                    nom: 'TEST_CORRIGE',
                    prenom: 'Interface',
                    email: `test.corrige.${Date.now()}@example.com`,
                    telephone: '0123456789'
                };

                const response = await fetch('/api/clients', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json; charset=utf-8'
                    },
                    body: JSON.stringify(clientData)
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }

                const result = await response.json();
                
                testResults.ajout = true;
                updateStatus('statusAjout', 'Ajout OK', 'ok');
                
                addResult('addResult', 'success', `✅ CLIENT AJOUTÉ AVEC SUCCÈS
ID: ${result.id}
Message: ${result.message}

L'interface devrait maintenant afficher ce nouveau client.`);
                
            } catch (error) {
                testResults.ajout = false;
                updateStatus('statusAjout', 'Ajout ERREUR', 'error');
                addResult('addResult', 'error', `❌ Erreur ajout: ${error.message}`);
            }
        }

        // Utilitaires
        function updateStatus(elementId, text, type) {
            const element = document.getElementById(elementId);
            element.textContent = text;
            element.className = `status-card status-${type}`;
        }

        function addResult(containerId, type, message) {
            const container = document.getElementById(containerId);
            container.className = `result ${type}`;
            container.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
        }

        // Auto-start
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🔧 Page de test interface corrigée chargée');
            testAPI(); // Test automatique de l'API
        });
    </script>
</body>
</html>
