<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Upload Photo</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        button { padding: 10px 20px; margin: 5px; }
        .result { margin: 10px 0; padding: 10px; background: #f0f0f0; }
    </style>
</head>
<body>
    <h1>🧪 Test Upload Photo</h1>
    
    <div class="test-section">
        <h3>1. C<PERSON>er un client de test</h3>
        <button onclick="createTestClient()">Créer Client Test</button>
        <div id="createResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>2. Upload Photo</h3>
        <input type="file" id="photoFile" accept="image/*">
        <button onclick="uploadTestPhoto()">Upload Photo</button>
        <div id="uploadResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. Vérifier Client</h3>
        <button onclick="checkClient()">Vérifier Client</button>
        <div id="checkResult" class="result"></div>
    </div>

    <script>
        let testClientId = null;

        async function createTestClient() {
            const clientData = {
                nom: 'TEST',
                prenom: 'Photo',
                email: '<EMAIL>',
                telephone: '0123456789'
            };

            try {
                const response = await fetch('/api/clients', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(clientData)
                });

                const result = await response.json();
                testClientId = result.id;
                document.getElementById('createResult').innerHTML = 
                    `✅ Client créé avec ID: ${testClientId}`;
            } catch (error) {
                document.getElementById('createResult').innerHTML = 
                    `❌ Erreur: ${error.message}`;
            }
        }

        async function uploadTestPhoto() {
            if (!testClientId) {
                alert('Créez d\'abord un client de test');
                return;
            }

            const fileInput = document.getElementById('photoFile');
            if (!fileInput.files[0]) {
                alert('Sélectionnez une photo');
                return;
            }

            const formData = new FormData();
            formData.append('photo', fileInput.files[0]);

            try {
                const response = await fetch(`/api/upload/photo/${testClientId}`, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                document.getElementById('uploadResult').innerHTML = 
                    `✅ Photo uploadée: ${result.photoUrl}`;
            } catch (error) {
                document.getElementById('uploadResult').innerHTML = 
                    `❌ Erreur: ${error.message}`;
            }
        }

        async function checkClient() {
            if (!testClientId) {
                alert('Créez d\'abord un client de test');
                return;
            }

            try {
                const response = await fetch(`/api/clients/${testClientId}`);
                const client = await response.json();
                
                document.getElementById('checkResult').innerHTML = 
                    `<strong>Client:</strong> ${client.nom} ${client.prenom}<br>
                     <strong>Photo URL:</strong> ${client.photo_url || 'Aucune'}<br>
                     ${client.photo_url ? `<img src="${client.photo_url}" style="max-width: 100px; max-height: 100px;">` : ''}`;
            } catch (error) {
                document.getElementById('checkResult').innerHTML = 
                    `❌ Erreur: ${error.message}`;
            }
        }
    </script>
</body>
</html>
