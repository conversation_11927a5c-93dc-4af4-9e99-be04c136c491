{"name": "gestion-clients", "version": "1.0.0", "description": "Système de gestion de clients avec Express.js et SQLite", "main": "backend/server.js", "scripts": {"start": "node backend/server.js", "dev": "nodemon backend/server.js"}, "dependencies": {"body-parser": "^1.20.2", "cors": "^2.8.5", "express": "^4.18.2", "fs-extra": "^11.1.1", "multer": "^1.4.5-lts.1", "path": "^0.12.7", "sqlite3": "^5.1.6", "xlsx": "^0.18.5"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["clients", "gestion", "express", "sqlite"], "author": "Ham<PERSON>", "license": "MIT"}