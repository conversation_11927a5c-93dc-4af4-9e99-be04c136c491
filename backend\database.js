const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// <PERSON><PERSON>er ou ouvrir la base de données
const dbPath = path.join(__dirname, 'clients.db');
const db = new sqlite3.Database(dbPath);

// Initialiser les tables
db.serialize(() => {
    // Table clients avec photo
    db.run(`CREATE TABLE IF NOT EXISTS clients (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        nom TEXT NOT NULL,
        prenom TEXT NOT NULL,
        email TEXT UNIQUE NOT NULL,
        telephone TEXT,
        adresse TEXT,
        ville TEXT,
        code_postal TEXT,
        photo_url TEXT,
        date_creation DATETIME DEFAULT CURRENT_TIMESTAMP,
        date_modification DATETIME DEFAULT CURRENT_TIMESTAMP
    )`);

    // Table documents pour stocker les documents des clients
    db.run(`CREATE TABLE IF NOT EXISTS documents (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        client_id INTEGER NOT NULL,
        type_document TEXT NOT NULL,
        nom_fichier TEXT NOT NULL,
        chemin_fichier TEXT NOT NULL,
        taille_fichier INTEGER,
        date_upload DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (client_id) REFERENCES clients (id) ON DELETE CASCADE
    )`);
});

// Fonctions pour gérer les clients
const clientsDB = {
    // Obtenir tous les clients
    getAllClients: (callback) => {
        db.all("SELECT * FROM clients ORDER BY nom, prenom", callback);
    },

    // Obtenir un client par ID
    getClientById: (id, callback) => {
        db.get("SELECT * FROM clients WHERE id = ?", [id], callback);
    },

    // Ajouter un nouveau client
    addClient: (client, callback) => {
        const { nom, prenom, email, telephone, adresse, ville, code_postal, photo_url } = client;
        db.run(
            `INSERT INTO clients (nom, prenom, email, telephone, adresse, ville, code_postal, photo_url)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
            [nom, prenom, email, telephone, adresse, ville, code_postal, photo_url],
            function(err) {
                callback(err, this.lastID);
            }
        );
    },

    // Mettre à jour un client
    updateClient: (id, client, callback) => {
        // Si seulement photo_url est fourni, ne mettre à jour que la photo
        if (Object.keys(client).length === 1 && client.photo_url !== undefined) {
            db.run(
                `UPDATE clients SET photo_url = ?, date_modification = CURRENT_TIMESTAMP WHERE id = ?`,
                [client.photo_url, id],
                callback
            );
            return;
        }

        const { nom, prenom, email, telephone, adresse, ville, code_postal, photo_url } = client;
        db.run(
            `UPDATE clients SET
             nom = ?, prenom = ?, email = ?, telephone = ?,
             adresse = ?, ville = ?, code_postal = ?, photo_url = ?,
             date_modification = CURRENT_TIMESTAMP
             WHERE id = ?`,
            [nom, prenom, email, telephone, adresse, ville, code_postal, photo_url, id],
            callback
        );
    },

    // Supprimer un client
    deleteClient: (id, callback) => {
        db.run("DELETE FROM clients WHERE id = ?", [id], callback);
    },

    // Rechercher des clients
    searchClients: (searchTerm, callback) => {
        const term = `%${searchTerm}%`;
        db.all(
            `SELECT * FROM clients
             WHERE nom LIKE ? OR prenom LIKE ? OR email LIKE ? OR ville LIKE ?
             ORDER BY nom, prenom`,
            [term, term, term, term],
            callback
        );
    },

    // Fonctions pour gérer les documents
    // Ajouter un document
    addDocument: (document, callback) => {
        const { client_id, type_document, nom_fichier, chemin_fichier, taille_fichier } = document;
        db.run(
            `INSERT INTO documents (client_id, type_document, nom_fichier, chemin_fichier, taille_fichier)
             VALUES (?, ?, ?, ?, ?)`,
            [client_id, type_document, nom_fichier, chemin_fichier, taille_fichier],
            function(err) {
                callback(err, this.lastID);
            }
        );
    },

    // Obtenir tous les documents d'un client
    getClientDocuments: (clientId, callback) => {
        db.all("SELECT * FROM documents WHERE client_id = ? ORDER BY date_upload DESC", [clientId], callback);
    },

    // Supprimer un document
    deleteDocument: (documentId, callback) => {
        db.get("SELECT * FROM documents WHERE id = ?", [documentId], (err, document) => {
            if (err) return callback(err);
            if (!document) return callback(new Error('Document non trouvé'));

            db.run("DELETE FROM documents WHERE id = ?", [documentId], (err) => {
                callback(err, document);
            });
        });
    },

    // Obtenir un document par ID
    getDocumentById: (documentId, callback) => {
        db.get("SELECT * FROM documents WHERE id = ?", [documentId], callback);
    }
};

module.exports = clientsDB;
