const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// <PERSON><PERSON>er ou ouvrir la base de données
const dbPath = path.join(__dirname, 'clients.db');
const db = new sqlite3.Database(dbPath);

// Initialiser les tables
db.serialize(() => {
    // Table clients avec photo
    db.run(`CREATE TABLE IF NOT EXISTS clients (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        nom TEXT NOT NULL,
        prenom TEXT NOT NULL,
        email TEXT UNIQUE NOT NULL,
        telephone TEXT,
        adresse TEXT,
        ville TEXT,
        code_postal TEXT,
        photo_url TEXT,
        date_creation DATETIME DEFAULT CURRENT_TIMESTAMP,
        date_modification DATETIME DEFAULT CURRENT_TIMESTAMP
    )`);

    // Table documents pour stocker les documents des clients
    db.run(`CREATE TABLE IF NOT EXISTS documents (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        client_id INTEGER NOT NULL,
        type_document TEXT NOT NULL,
        nom_fichier TEXT NOT NULL,
        chemin_fichier TEXT NOT NULL,
        taille_fichier INTEGER,
        date_upload DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (client_id) REFERENCES clients (id) ON DELETE CASCADE
    )`);
});

// Fonctions pour gérer les clients
const clientsDB = {
    // Obtenir tous les clients
    getAllClients: (callback) => {
        db.all("SELECT * FROM clients ORDER BY nom, prenom", callback);
    },

    // Obtenir un client par ID
    getClientById: (id, callback) => {
        db.get("SELECT * FROM clients WHERE id = ?", [id], callback);
    },

    // Ajouter un nouveau client
    addClient: (client, callback) => {
        const {
            nom, prenom, email, telephone, adresse, ville, code_postal, photo_url,
            cin_no, passeport_no, date_expiration_passeport, nationality_id, sexe_id,
            situation_familiale_id, date_de_naissance, pays_de_naissance_id, lieu_de_naissance_id,
            label_tel_id, mobile_1, label_mobile_1_id, mobile_2, label_mobile_2_id,
            nom_arabe, prenom_arabe, loyalty_card_no, airline_code_for_loyalty_card_id,
            related_name, label_related_name_id, tel_related_name, compte_comptable_id
        } = client;

        db.run(
            `INSERT INTO clients (
                nom, prenom, email, telephone, adresse, ville, code_postal, photo_url,
                cin_no, passeport_no, date_expiration_passeport, nationality_id, sexe_id,
                situation_familiale_id, date_de_naissance, pays_de_naissance_id, lieu_de_naissance_id,
                label_tel_id, mobile_1, label_mobile_1_id, mobile_2, label_mobile_2_id,
                nom_arabe, prenom_arabe, loyalty_card_no, airline_code_for_loyalty_card_id,
                related_name, label_related_name_id, tel_related_name, compte_comptable_id
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
                nom, prenom, email, telephone, adresse, ville, code_postal, photo_url,
                cin_no, passeport_no, date_expiration_passeport, nationality_id, sexe_id,
                situation_familiale_id, date_de_naissance, pays_de_naissance_id, lieu_de_naissance_id,
                label_tel_id, mobile_1, label_mobile_1_id, mobile_2, label_mobile_2_id,
                nom_arabe, prenom_arabe, loyalty_card_no, airline_code_for_loyalty_card_id,
                related_name, label_related_name_id, tel_related_name, compte_comptable_id
            ],
            function(err) {
                callback(err, this.lastID);
            }
        );
    },

    // Mettre à jour un client
    updateClient: (id, client, callback) => {
        console.log('🔄 updateClient appelé avec:', { id, client });

        // Si seulement photo_url est fourni, ne mettre à jour que la photo
        if (Object.keys(client).length === 1 && client.photo_url !== undefined) {
            console.log('📸 Mise à jour photo uniquement:', client.photo_url);
            db.run(
                `UPDATE clients SET photo_url = ?, date_modification = CURRENT_TIMESTAMP WHERE id = ?`,
                [client.photo_url, id],
                (err) => {
                    if (err) console.error('❌ Erreur mise à jour photo:', err);
                    else console.log('✅ Photo mise à jour avec succès');
                    callback(err);
                }
            );
            return;
        }

        // Pour une mise à jour complète, d'abord récupérer les données existantes
        console.log('🔄 Mise à jour complète du client');
        db.get("SELECT * FROM clients WHERE id = ?", [id], (err, existingClient) => {
            if (err) {
                console.error('❌ Erreur récupération client existant:', err);
                return callback(err);
            }
            if (!existingClient) {
                console.error('❌ Client non trouvé:', id);
                return callback(new Error('Client non trouvé'));
            }

            console.log('📋 Client existant:', existingClient);

            const {
                nom, prenom, email, telephone, adresse, ville, code_postal, photo_url,
                cin_no, passeport_no, date_expiration_passeport, nationality_id, sexe_id,
                situation_familiale_id, date_de_naissance, pays_de_naissance_id, lieu_de_naissance_id,
                label_tel_id, mobile_1, label_mobile_1_id, mobile_2, label_mobile_2_id,
                nom_arabe, prenom_arabe, loyalty_card_no, airline_code_for_loyalty_card_id,
                related_name, label_related_name_id, tel_related_name, compte_comptable_id
            } = client;

            // Utiliser les valeurs existantes si aucune nouvelle valeur n'est fournie
            const finalData = {
                nom: nom !== undefined ? nom : existingClient.nom,
                prenom: prenom !== undefined ? prenom : existingClient.prenom,
                email: email !== undefined ? email : existingClient.email,
                telephone: telephone !== undefined ? telephone : existingClient.telephone,
                adresse: adresse !== undefined ? adresse : existingClient.adresse,
                ville: ville !== undefined ? ville : existingClient.ville,
                code_postal: code_postal !== undefined ? code_postal : existingClient.code_postal,
                photo_url: photo_url !== undefined ? photo_url : existingClient.photo_url,
                cin_no: cin_no !== undefined ? cin_no : existingClient.cin_no,
                passeport_no: passeport_no !== undefined ? passeport_no : existingClient.passeport_no,
                date_expiration_passeport: date_expiration_passeport !== undefined ? date_expiration_passeport : existingClient.date_expiration_passeport,
                nationality_id: nationality_id !== undefined ? nationality_id : existingClient.nationality_id,
                sexe_id: sexe_id !== undefined ? sexe_id : existingClient.sexe_id,
                situation_familiale_id: situation_familiale_id !== undefined ? situation_familiale_id : existingClient.situation_familiale_id,
                date_de_naissance: date_de_naissance !== undefined ? date_de_naissance : existingClient.date_de_naissance,
                pays_de_naissance_id: pays_de_naissance_id !== undefined ? pays_de_naissance_id : existingClient.pays_de_naissance_id,
                lieu_de_naissance_id: lieu_de_naissance_id !== undefined ? lieu_de_naissance_id : existingClient.lieu_de_naissance_id,
                label_tel_id: label_tel_id !== undefined ? label_tel_id : existingClient.label_tel_id,
                mobile_1: mobile_1 !== undefined ? mobile_1 : existingClient.mobile_1,
                label_mobile_1_id: label_mobile_1_id !== undefined ? label_mobile_1_id : existingClient.label_mobile_1_id,
                mobile_2: mobile_2 !== undefined ? mobile_2 : existingClient.mobile_2,
                label_mobile_2_id: label_mobile_2_id !== undefined ? label_mobile_2_id : existingClient.label_mobile_2_id,
                nom_arabe: nom_arabe !== undefined ? nom_arabe : existingClient.nom_arabe,
                prenom_arabe: prenom_arabe !== undefined ? prenom_arabe : existingClient.prenom_arabe,
                loyalty_card_no: loyalty_card_no !== undefined ? loyalty_card_no : existingClient.loyalty_card_no,
                airline_code_for_loyalty_card_id: airline_code_for_loyalty_card_id !== undefined ? airline_code_for_loyalty_card_id : existingClient.airline_code_for_loyalty_card_id,
                related_name: related_name !== undefined ? related_name : existingClient.related_name,
                label_related_name_id: label_related_name_id !== undefined ? label_related_name_id : existingClient.label_related_name_id,
                tel_related_name: tel_related_name !== undefined ? tel_related_name : existingClient.tel_related_name,
                compte_comptable_id: compte_comptable_id !== undefined ? compte_comptable_id : existingClient.compte_comptable_id
            };

            console.log('💾 Données finales:', finalData);

            // Vérifier que les champs obligatoires sont présents
            if (!finalData.nom || !finalData.prenom || !finalData.email) {
                const error = new Error('Champs obligatoires manquants');
                console.error('❌', error.message);
                return callback(error);
            }

            db.run(
                `UPDATE clients SET
                 nom = ?, prenom = ?, email = ?, telephone = ?, adresse = ?, ville = ?, code_postal = ?, photo_url = ?,
                 cin_no = ?, passeport_no = ?, date_expiration_passeport = ?, nationality_id = ?, sexe_id = ?,
                 situation_familiale_id = ?, date_de_naissance = ?, pays_de_naissance_id = ?, lieu_de_naissance_id = ?,
                 label_tel_id = ?, mobile_1 = ?, label_mobile_1_id = ?, mobile_2 = ?, label_mobile_2_id = ?,
                 nom_arabe = ?, prenom_arabe = ?, loyalty_card_no = ?, airline_code_for_loyalty_card_id = ?,
                 related_name = ?, label_related_name_id = ?, tel_related_name = ?, compte_comptable_id = ?,
                 date_modification = CURRENT_TIMESTAMP
                 WHERE id = ?`,
                [
                    finalData.nom, finalData.prenom, finalData.email, finalData.telephone, finalData.adresse,
                    finalData.ville, finalData.code_postal, finalData.photo_url, finalData.cin_no, finalData.passeport_no,
                    finalData.date_expiration_passeport, finalData.nationality_id, finalData.sexe_id, finalData.situation_familiale_id,
                    finalData.date_de_naissance, finalData.pays_de_naissance_id, finalData.lieu_de_naissance_id, finalData.label_tel_id,
                    finalData.mobile_1, finalData.label_mobile_1_id, finalData.mobile_2, finalData.label_mobile_2_id,
                    finalData.nom_arabe, finalData.prenom_arabe, finalData.loyalty_card_no, finalData.airline_code_for_loyalty_card_id,
                    finalData.related_name, finalData.label_related_name_id, finalData.tel_related_name, finalData.compte_comptable_id,
                    id
                ],
                (err) => {
                    if (err) {
                        console.error('❌ Erreur SQL mise à jour:', err);
                    } else {
                        console.log('✅ Client mis à jour avec succès');
                    }
                    callback(err);
                }
            );
        });
    },

    // Supprimer un client
    deleteClient: (id, callback) => {
        db.run("DELETE FROM clients WHERE id = ?", [id], callback);
    },

    // Rechercher des clients
    searchClients: (searchTerm, callback) => {
        const term = `%${searchTerm}%`;
        db.all(
            `SELECT * FROM clients
             WHERE nom LIKE ? OR prenom LIKE ? OR email LIKE ? OR ville LIKE ?
             ORDER BY nom, prenom`,
            [term, term, term, term],
            callback
        );
    },

    // Fonctions pour gérer les documents
    // Ajouter un document
    addDocument: (document, callback) => {
        const { client_id, type_document, nom_fichier, chemin_fichier, taille_fichier } = document;
        db.run(
            `INSERT INTO documents (client_id, type_document, nom_fichier, chemin_fichier, taille_fichier)
             VALUES (?, ?, ?, ?, ?)`,
            [client_id, type_document, nom_fichier, chemin_fichier, taille_fichier],
            function(err) {
                callback(err, this.lastID);
            }
        );
    },

    // Obtenir tous les documents d'un client
    getClientDocuments: (clientId, callback) => {
        db.all("SELECT * FROM documents WHERE client_id = ? ORDER BY date_upload DESC", [clientId], callback);
    },

    // Supprimer un document
    deleteDocument: (documentId, callback) => {
        db.get("SELECT * FROM documents WHERE id = ?", [documentId], (err, document) => {
            if (err) return callback(err);
            if (!document) return callback(new Error('Document non trouvé'));

            db.run("DELETE FROM documents WHERE id = ?", [documentId], (err) => {
                callback(err, document);
            });
        });
    },

    // Obtenir un document par ID
    getDocumentById: (documentId, callback) => {
        db.get("SELECT * FROM documents WHERE id = ?", [documentId], callback);
    },

    // Fonctions pour les tables de référence
    getAllNationalities: (callback) => {
        db.all("SELECT * FROM nationality ORDER BY nom_fr", callback);
    },

    getAllSexes: (callback) => {
        db.all("SELECT * FROM sexe ORDER BY libelle_fr", callback);
    },

    getAllSituationsFamiliales: (callback) => {
        db.all("SELECT * FROM situation_familiale ORDER BY libelle_fr", callback);
    },

    getAllPaysDeNaissance: (callback) => {
        db.all("SELECT * FROM pays_de_naissance ORDER BY nom_fr", callback);
    },

    getAllLieuxDeNaissance: (callback) => {
        db.all("SELECT * FROM lieu_de_naissance ORDER BY nom_fr", callback);
    },

    getAllLabelsTel: (callback) => {
        db.all("SELECT * FROM label_tel ORDER BY libelle", callback);
    },

    getAllLabelsMobile: (callback) => {
        db.all("SELECT * FROM label_mobile ORDER BY libelle", callback);
    },

    getAllAirlineCodes: (callback) => {
        db.all("SELECT * FROM airline_code_for_loyalty_card ORDER BY nom", callback);
    },

    getAllLabelsRelatedName: (callback) => {
        db.all("SELECT * FROM label_related_name ORDER BY libelle", callback);
    },

    getAllComptesComptables: (callback) => {
        db.all("SELECT * FROM compte_comptable ORDER BY numero_compte", callback);
    },

    // Fonction pour obtenir un client avec toutes ses relations
    getClientWithRelations: (id, callback) => {
        db.get(`
            SELECT c.*,
                   n.nom_fr as nationality_name,
                   s.libelle_fr as sexe_name,
                   sf.libelle_fr as situation_familiale_name,
                   pn.nom_fr as pays_naissance_name,
                   ln.nom_fr as lieu_naissance_name,
                   lt.libelle as label_tel_name,
                   lm1.libelle as label_mobile_1_name,
                   lm2.libelle as label_mobile_2_name,
                   ac.nom as airline_name,
                   lrn.libelle as label_related_name_name,
                   cc.libelle as compte_comptable_name
            FROM clients c
            LEFT JOIN nationality n ON c.nationality_id = n.id
            LEFT JOIN sexe s ON c.sexe_id = s.id
            LEFT JOIN situation_familiale sf ON c.situation_familiale_id = sf.id
            LEFT JOIN pays_de_naissance pn ON c.pays_de_naissance_id = pn.id
            LEFT JOIN lieu_de_naissance ln ON c.lieu_de_naissance_id = ln.id
            LEFT JOIN label_tel lt ON c.label_tel_id = lt.id
            LEFT JOIN label_mobile lm1 ON c.label_mobile_1_id = lm1.id
            LEFT JOIN label_mobile lm2 ON c.label_mobile_2_id = lm2.id
            LEFT JOIN airline_code_for_loyalty_card ac ON c.airline_code_for_loyalty_card_id = ac.id
            LEFT JOIN label_related_name lrn ON c.label_related_name_id = lrn.id
            LEFT JOIN compte_comptable cc ON c.compte_comptable_id = cc.id
            WHERE c.id = ?
        `, [id], callback);
    }
};

module.exports = clientsDB;
