const express = require('express');
const router = express.Router();
const XLSX = require('xlsx');
const clientsDB = require('../database');

// GET /api/clients/stats - Obtenir les statistiques des clients (AVANT les autres routes)
router.get('/stats', (req, res) => {
    clientsDB.getAllClients((err, clients) => {
        if (err) {
            return res.status(500).json({ error: 'Erreur lors de la récupération des statistiques' });
        }

        const now = new Date();
        const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

        const stats = {
            total: clients.length,
            thisMonth: clients.filter(client => {
                const createdDate = new Date(client.date_creation);
                return createdDate >= thisMonth;
            }).length,
            thisYear: clients.filter(client => {
                const createdDate = new Date(client.date_creation);
                return createdDate.getFullYear() === now.getFullYear();
            }).length,
            byNationality: {},
            bySexe: {},
            averageAge: 0
        };

        // Statistiques par nationalité
        clients.forEach(client => {
            if (client.nationality_name) {
                stats.byNationality[client.nationality_name] =
                    (stats.byNationality[client.nationality_name] || 0) + 1;
            }
        });

        // Statistiques par sexe
        clients.forEach(client => {
            if (client.sexe_name) {
                stats.bySexe[client.sexe_name] =
                    (stats.bySexe[client.sexe_name] || 0) + 1;
            }
        });

        // Âge moyen
        const clientsWithAge = clients.filter(client => client.date_de_naissance);
        if (clientsWithAge.length > 0) {
            const totalAge = clientsWithAge.reduce((sum, client) => {
                const birthDate = new Date(client.date_de_naissance);
                const age = now.getFullYear() - birthDate.getFullYear();
                return sum + age;
            }, 0);
            stats.averageAge = Math.round(totalAge / clientsWithAge.length);
        }

        res.json(stats);
    });
});

// GET /api/clients - Obtenir tous les clients
router.get('/', (req, res) => {
    clientsDB.getAllClients((err, clients) => {
        if (err) {
            return res.status(500).json({ error: 'Erreur lors de la récupération des clients' });
        }
        res.json(clients);
    });
});

// GET /api/clients/search?q=terme - Rechercher des clients
router.get('/search', (req, res) => {
    const searchTerm = req.query.q;
    if (!searchTerm) {
        return res.status(400).json({ error: 'Terme de recherche requis' });
    }

    clientsDB.searchClients(searchTerm, (err, clients) => {
        if (err) {
            return res.status(500).json({ error: 'Erreur lors de la recherche' });
        }
        res.json(clients);
    });
});

// GET /api/clients/:id - Obtenir un client par ID
router.get('/:id', (req, res) => {
    const id = req.params.id;
    clientsDB.getClientWithRelations(id, (err, client) => {
        if (err) {
            return res.status(500).json({ error: 'Erreur lors de la récupération du client' });
        }
        if (!client) {
            return res.status(404).json({ error: 'Client non trouvé' });
        }
        res.json(client);
    });
});

// POST /api/clients - Ajouter un nouveau client
router.post('/', (req, res) => {
    const clientData = req.body;

    console.log('➕ POST /api/clients');
    console.log('📋 Données reçues:', JSON.stringify(clientData, null, 2));

    // Validation des champs requis
    if (!clientData.nom || !clientData.prenom || !clientData.email) {
        console.error('❌ Champs obligatoires manquants');
        return res.status(400).json({ error: 'Nom, prénom et email sont requis' });
    }

    console.log('✅ Validation réussie, appel addClient...');

    clientsDB.addClient(clientData, (err, clientId) => {
        if (err) {
            console.error('❌ Erreur addClient:', err);
            if (err.code === 'SQLITE_CONSTRAINT_UNIQUE') {
                return res.status(400).json({ error: 'Un client avec cet email existe déjà' });
            }
            return res.status(500).json({ error: 'Erreur lors de l\'ajout du client: ' + err.message });
        }
        console.log('✅ Client ajouté avec succès, ID:', clientId);
        res.status(201).json({ id: clientId, message: 'Client ajouté avec succès' });
    });
});

// PUT /api/clients/:id - Mettre à jour un client
router.put('/:id', (req, res) => {
    const id = req.params.id;
    const clientData = req.body;

    console.log(`🔄 PUT /api/clients/${id}`);
    console.log('📋 Données reçues:', JSON.stringify(clientData, null, 2));

    // Validation des données
    if (!clientData || typeof clientData !== 'object') {
        console.error('❌ Données invalides reçues');
        return res.status(400).json({ error: 'Données invalides' });
    }

    // Validation des champs obligatoires
    if (!clientData.nom || !clientData.prenom || !clientData.email) {
        console.error('❌ Champs obligatoires manquants:', {
            nom: !!clientData.nom,
            prenom: !!clientData.prenom,
            email: !!clientData.email
        });
        return res.status(400).json({ error: 'Les champs nom, prénom et email sont obligatoires' });
    }

    console.log('✅ Validation réussie, appel updateClient...');

    clientsDB.updateClient(id, clientData, (err) => {
        if (err) {
            console.error('❌ Erreur updateClient:', err);
            if (err.code === 'SQLITE_CONSTRAINT_UNIQUE') {
                return res.status(400).json({ error: 'Un client avec cet email existe déjà' });
            }
            return res.status(500).json({ error: 'Erreur lors de la mise à jour du client: ' + err.message });
        }
        console.log('✅ Client mis à jour avec succès');
        res.json({ message: 'Client mis à jour avec succès', id: id });
    });
});

// DELETE /api/clients/:id - Supprimer un client
router.delete('/:id', (req, res) => {
    const id = req.params.id;

    clientsDB.deleteClient(id, (err) => {
        if (err) {
            return res.status(500).json({ error: 'Erreur lors de la suppression du client' });
        }
        res.json({ message: 'Client supprimé avec succès' });
    });
});

// GET /api/clients/export/excel - Exporter tous les clients en Excel
router.get('/export/excel', (req, res) => {
    console.log('📤 Export Excel demandé...');

    // Récupérer tous les clients avec leurs relations
    clientsDB.getAllClients((err, clients) => {
        if (err) {
            console.error('❌ Erreur récupération clients pour export:', err);
            return res.status(500).json({ error: 'Erreur lors de la récupération des clients' });
        }

        console.log(`📋 ${clients.length} clients à exporter`);

        try {
            // Préparer les données pour Excel avec tous les champs
            const excelData = clients.map(client => ({
                'Code Client': client.code_client || '',
                'Nom': client.nom || '',
                'Prénom': client.prenom || '',
                'Email': client.email || '',
                'Téléphone': client.telephone || '',
                'Mobile 1': client.mobile_1 || '',
                'Mobile 2': client.mobile_2 || '',
                'Adresse': client.adresse || '',
                'Ville': client.ville || '',
                'Code Postal': client.code_postal || '',
                'CIN N°': client.cin_no || '',
                'Passeport N°': client.passeport_no || '',
                'Date Expiration Passeport': client.date_expiration_passeport || '',
                'Date de Naissance': client.date_de_naissance || '',
                'Nationalité ID': client.nationality_id || '',
                'Sexe ID': client.sexe_id || '',
                'Situation Familiale ID': client.situation_familiale_id || '',
                'Pays de Naissance ID': client.pays_de_naissance_id || '',
                'Lieu de Naissance ID': client.lieu_de_naissance_id || '',
                'Compte Comptable ID': client.compte_comptable_id || '',
                'Label Tél ID': client.label_tel_id || '',
                'Label Mobile 1 ID': client.label_mobile_1_id || '',
                'Label Mobile 2 ID': client.label_mobile_2_id || '',
                'Nom Arabe': client.nom_arabe || '',
                'Prénom Arabe': client.prenom_arabe || '',
                'N° Carte Fidélité': client.loyalty_card_no || '',
                'Compagnie Aérienne ID': client.airline_code_for_loyalty_card_id || '',
                'Nom Personne Liée': client.related_name || '',
                'Label Personne Liée ID': client.label_related_name_id || '',
                'Tél Personne Liée': client.tel_related_name || '',
                'Photo URL': client.photo_url || '',
                'Date Création': client.date_creation || '',
                'Date Modification': client.date_modification || ''
            }));

            // Créer le workbook Excel
            const workbook = XLSX.utils.book_new();
            const worksheet = XLSX.utils.json_to_sheet(excelData);

            // Ajuster la largeur des colonnes
            const columnWidths = [
                { wch: 12 }, // Code Client
                { wch: 15 }, // Nom
                { wch: 15 }, // Prénom
                { wch: 25 }, // Email
                { wch: 15 }, // Téléphone
                { wch: 15 }, // Mobile 1
                { wch: 15 }, // Mobile 2
                { wch: 30 }, // Adresse
                { wch: 15 }, // Ville
                { wch: 10 }, // Code Postal
                { wch: 12 }, // CIN N°
                { wch: 12 }, // Passeport N°
                { wch: 12 }, // Date Expiration
                { wch: 12 }, // Date Naissance
                { wch: 10 }, // Nationalité ID
                { wch: 8 },  // Sexe ID
                { wch: 12 }, // Situation Familiale ID
                { wch: 12 }, // Pays Naissance ID
                { wch: 12 }, // Lieu Naissance ID
                { wch: 12 }, // Compte Comptable ID
                { wch: 10 }, // Label Tél ID
                { wch: 12 }, // Label Mobile 1 ID
                { wch: 12 }, // Label Mobile 2 ID
                { wch: 15 }, // Nom Arabe
                { wch: 15 }, // Prénom Arabe
                { wch: 15 }, // N° Carte Fidélité
                { wch: 12 }, // Compagnie Aérienne ID
                { wch: 20 }, // Nom Personne Liée
                { wch: 12 }, // Label Personne Liée ID
                { wch: 15 }, // Tél Personne Liée
                { wch: 30 }, // Photo URL
                { wch: 18 }, // Date Création
                { wch: 18 }  // Date Modification
            ];
            worksheet['!cols'] = columnWidths;

            // Ajouter la feuille au workbook
            XLSX.utils.book_append_sheet(workbook, worksheet, 'Clients');

            // Générer le fichier Excel en buffer
            const excelBuffer = XLSX.write(workbook, {
                type: 'buffer',
                bookType: 'xlsx',
                compression: true
            });

            // Définir les headers pour le téléchargement
            const fileName = `clients_export_${new Date().toISOString().split('T')[0]}.xlsx`;

            res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
            res.setHeader('Content-Length', excelBuffer.length);

            console.log(`✅ Export Excel généré: ${fileName} (${excelBuffer.length} bytes)`);

            // Envoyer le fichier
            res.send(excelBuffer);

        } catch (error) {
            console.error('❌ Erreur génération Excel:', error);
            res.status(500).json({ error: 'Erreur lors de la génération du fichier Excel: ' + error.message });
        }
    });
});

module.exports = router;
