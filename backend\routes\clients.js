const express = require('express');
const router = express.Router();
const XLSX = require('xlsx');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const clientsDB = require('../database');

// Configuration multer pour l'import Excel
const importStorage = multer.diskStorage({
    destination: function (req, file, cb) {
        const importDir = path.join(__dirname, '../uploads/imports');
        if (!fs.existsSync(importDir)) {
            fs.mkdirSync(importDir, { recursive: true });
        }
        cb(null, importDir);
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, 'import-' + uniqueSuffix + path.extname(file.originalname));
    }
});

const importFilter = (req, file, cb) => {
    const allowedTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
        'application/vnd.ms-excel', // .xls
        'text/csv' // .csv
    ];
    if (allowedTypes.includes(file.mimetype) || file.originalname.endsWith('.xlsx') || file.originalname.endsWith('.xls') || file.originalname.endsWith('.csv')) {
        cb(null, true);
    } else {
        cb(new Error('Seuls les fichiers Excel (.xlsx, .xls) et CSV sont autorisés'), false);
    }
};

const uploadImport = multer({
    storage: importStorage,
    fileFilter: importFilter,
    limits: { fileSize: 50 * 1024 * 1024 } // 50MB max
});

// GET /api/clients/stats - Obtenir les statistiques des clients (AVANT les autres routes)
router.get('/stats', (req, res) => {
    clientsDB.getAllClients((err, clients) => {
        if (err) {
            return res.status(500).json({ error: 'Erreur lors de la récupération des statistiques' });
        }

        const now = new Date();
        const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

        const stats = {
            total: clients.length,
            thisMonth: clients.filter(client => {
                const createdDate = new Date(client.date_creation);
                return createdDate >= thisMonth;
            }).length,
            thisYear: clients.filter(client => {
                const createdDate = new Date(client.date_creation);
                return createdDate.getFullYear() === now.getFullYear();
            }).length,
            byNationality: {},
            bySexe: {},
            averageAge: 0
        };

        // Statistiques par nationalité
        clients.forEach(client => {
            if (client.nationality_name) {
                stats.byNationality[client.nationality_name] =
                    (stats.byNationality[client.nationality_name] || 0) + 1;
            }
        });

        // Statistiques par sexe
        clients.forEach(client => {
            if (client.sexe_name) {
                stats.bySexe[client.sexe_name] =
                    (stats.bySexe[client.sexe_name] || 0) + 1;
            }
        });

        // Âge moyen
        const clientsWithAge = clients.filter(client => client.date_de_naissance);
        if (clientsWithAge.length > 0) {
            const totalAge = clientsWithAge.reduce((sum, client) => {
                const birthDate = new Date(client.date_de_naissance);
                const age = now.getFullYear() - birthDate.getFullYear();
                return sum + age;
            }, 0);
            stats.averageAge = Math.round(totalAge / clientsWithAge.length);
        }

        res.json(stats);
    });
});

// GET /api/clients - Obtenir tous les clients
router.get('/', (req, res) => {
    clientsDB.getAllClients((err, clients) => {
        if (err) {
            return res.status(500).json({ error: 'Erreur lors de la récupération des clients' });
        }
        res.json(clients);
    });
});

// POST /api/clients/import/excel - Importer des clients depuis Excel
router.post('/import/excel', uploadImport.single('excelFile'), (req, res) => {
    console.log('📥 Import Excel demandé...');

    if (!req.file) {
        return res.status(400).json({ error: 'Aucun fichier Excel fourni' });
    }

    console.log('📁 Fichier reçu:', req.file.originalname, '(', req.file.size, 'bytes)');

    try {
        // Lire le fichier Excel
        const workbook = XLSX.readFile(req.file.path);
        const sheetName = workbook.SheetNames[0]; // Première feuille
        const worksheet = workbook.Sheets[sheetName];

        console.log('📊 Lecture de la feuille:', sheetName);

        // Convertir en JSON
        const rawData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

        if (rawData.length < 2) {
            fs.unlinkSync(req.file.path); // Supprimer le fichier temporaire
            return res.status(400).json({ error: 'Le fichier Excel doit contenir au moins une ligne de données' });
        }

        // Extraire les headers et les données
        const headers = rawData[0];
        const dataRows = rawData.slice(1);

        console.log('📋 Headers trouvés:', headers.length);
        console.log('📊 Lignes de données:', dataRows.length);

        // Mapper les données selon notre structure
        const clients = [];
        const errors = [];

        for (let i = 0; i < dataRows.length; i++) {
            const row = dataRows[i];
            const rowNumber = i + 2; // +2 car ligne 1 = headers, et index commence à 0

            try {
                // Mapper les colonnes (selon l'ordre de notre export)
                const client = {
                    nom: row[1] || null, // Nom
                    prenom: row[2] || null, // Prénom
                    email: row[3] || null, // Email
                    telephone: row[4] || null, // Téléphone
                    mobile_1: row[5] || null, // Mobile 1
                    mobile_2: row[6] || null, // Mobile 2
                    adresse: row[7] || null, // Adresse
                    ville: row[8] || null, // Ville
                    code_postal: row[9] || null, // Code Postal
                    cin_no: row[10] || null, // CIN N°
                    passeport_no: row[11] || null, // Passeport N°
                    date_expiration_passeport: row[12] ? formatExcelDate(row[12]) : null,
                    date_de_naissance: row[13] ? formatExcelDate(row[13]) : null,
                    nationality_id: row[14] || null, // Nationalité ID
                    sexe_id: row[15] || null, // Sexe ID
                    situation_familiale_id: row[16] || null, // Situation Familiale ID
                    pays_de_naissance_id: row[17] || null, // Pays de Naissance ID
                    lieu_de_naissance_id: row[18] || null, // Lieu de Naissance ID
                    compte_comptable_id: row[19] || null, // Compte Comptable ID
                    label_tel_id: row[20] || null, // Label Tél ID
                    label_mobile_1_id: row[21] || null, // Label Mobile 1 ID
                    label_mobile_2_id: row[22] || null, // Label Mobile 2 ID
                    nom_arabe: row[23] || null, // Nom Arabe
                    prenom_arabe: row[24] || null, // Prénom Arabe
                    loyalty_card_no: row[25] || null, // N° Carte Fidélité
                    airline_code_for_loyalty_card_id: row[26] || null, // Compagnie Aérienne ID
                    related_name: row[27] || null, // Nom Personne Liée
                    label_related_name_id: row[28] || null, // Label Personne Liée ID
                    tel_related_name: row[29] || null, // Tél Personne Liée
                    photo_url: row[30] || null // Photo URL
                };

                // Validation des champs obligatoires
                if (!client.nom || !client.prenom) {
                    errors.push(`Ligne ${rowNumber}: Nom et prénom sont obligatoires`);
                    continue;
                }

                // Nettoyer les valeurs vides
                Object.keys(client).forEach(key => {
                    if (client[key] === '' || client[key] === undefined) {
                        client[key] = null;
                    }
                });

                clients.push({ ...client, _rowNumber: rowNumber });

            } catch (error) {
                errors.push(`Ligne ${rowNumber}: Erreur de parsing - ${error.message}`);
            }
        }

        // Supprimer le fichier temporaire
        fs.unlinkSync(req.file.path);

        console.log(`✅ Parsing terminé: ${clients.length} clients valides, ${errors.length} erreurs`);

        // Retourner les données pour preview
        res.json({
            success: true,
            preview: {
                totalRows: dataRows.length,
                validClients: clients.length,
                errors: errors,
                clients: clients.slice(0, 5), // Aperçu des 5 premiers
                headers: headers
            }
        });

    } catch (error) {
        console.error('❌ Erreur parsing Excel:', error);

        // Supprimer le fichier temporaire en cas d'erreur
        if (req.file && fs.existsSync(req.file.path)) {
            fs.unlinkSync(req.file.path);
        }

        res.status(500).json({
            error: 'Erreur lors de la lecture du fichier Excel: ' + error.message
        });
    }
});

// Fonction utilitaire pour formater les dates Excel
function formatExcelDate(excelDate) {
    if (!excelDate) return null;

    // Si c'est déjà une chaîne de date
    if (typeof excelDate === 'string') {
        return excelDate;
    }

    // Si c'est un nombre Excel (jours depuis 1900-01-01)
    if (typeof excelDate === 'number') {
        const date = new Date((excelDate - 25569) * 86400 * 1000);
        return date.toISOString().split('T')[0]; // Format YYYY-MM-DD
    }

    return null;
}

// POST /api/clients/import/confirm - Confirmer et importer les clients
router.post('/import/confirm', (req, res) => {
    console.log('✅ Confirmation d\'import reçue...');

    const { clients } = req.body;

    if (!clients || !Array.isArray(clients)) {
        return res.status(400).json({ error: 'Données clients invalides' });
    }

    console.log(`📊 Import de ${clients.length} clients confirmé`);

    let imported = 0;
    let errors = [];

    // Fonction pour importer un client
    const importClient = (client, callback) => {
        // Supprimer le champ _rowNumber utilisé pour le tracking
        const { _rowNumber, ...clientData } = client;

        clientsDB.addClient(clientData, (err, clientId) => {
            if (err) {
                const errorMsg = `Ligne ${_rowNumber}: ${err.message}`;
                errors.push(errorMsg);
                console.warn('⚠️', errorMsg);
            } else {
                imported++;
                console.log(`✅ Client importé: ${clientData.nom} ${clientData.prenom} (ID: ${clientId})`);
            }
            callback();
        });
    };

    // Importer les clients un par un (pour éviter les conflits)
    let currentIndex = 0;

    const importNext = () => {
        if (currentIndex >= clients.length) {
            // Import terminé
            console.log(`🎯 Import terminé: ${imported} succès, ${errors.length} erreurs`);

            res.json({
                success: true,
                imported: imported,
                errors: errors,
                total: clients.length
            });
            return;
        }

        importClient(clients[currentIndex], () => {
            currentIndex++;
            // Petit délai pour éviter la surcharge
            setTimeout(importNext, 10);
        });
    };

    // Démarrer l'import
    importNext();
});

// GET /api/clients/export/excel - Exporter tous les clients en Excel (AVANT les routes avec paramètres)
router.get('/export/excel', (req, res) => {
    console.log('📤 Export Excel demandé...');

    // Récupérer tous les clients avec leurs relations
    clientsDB.getAllClients((err, clients) => {
        if (err) {
            console.error('❌ Erreur récupération clients pour export:', err);
            return res.status(500).json({ error: 'Erreur lors de la récupération des clients' });
        }

        console.log(`📋 ${clients.length} clients à exporter`);

        try {
            // Préparer les données pour Excel avec tous les champs
            const excelData = clients.map(client => ({
                'Code Client': client.code_client || '',
                'Nom': client.nom || '',
                'Prénom': client.prenom || '',
                'Email': client.email || '',
                'Téléphone': client.telephone || '',
                'Mobile 1': client.mobile_1 || '',
                'Mobile 2': client.mobile_2 || '',
                'Adresse': client.adresse || '',
                'Ville': client.ville || '',
                'Code Postal': client.code_postal || '',
                'CIN N°': client.cin_no || '',
                'Passeport N°': client.passeport_no || '',
                'Date Expiration Passeport': client.date_expiration_passeport || '',
                'Date de Naissance': client.date_de_naissance || '',
                'Nationalité ID': client.nationality_id || '',
                'Sexe ID': client.sexe_id || '',
                'Situation Familiale ID': client.situation_familiale_id || '',
                'Pays de Naissance ID': client.pays_de_naissance_id || '',
                'Lieu de Naissance ID': client.lieu_de_naissance_id || '',
                'Compte Comptable ID': client.compte_comptable_id || '',
                'Label Tél ID': client.label_tel_id || '',
                'Label Mobile 1 ID': client.label_mobile_1_id || '',
                'Label Mobile 2 ID': client.label_mobile_2_id || '',
                'Nom Arabe': client.nom_arabe || '',
                'Prénom Arabe': client.prenom_arabe || '',
                'N° Carte Fidélité': client.loyalty_card_no || '',
                'Compagnie Aérienne ID': client.airline_code_for_loyalty_card_id || '',
                'Nom Personne Liée': client.related_name || '',
                'Label Personne Liée ID': client.label_related_name_id || '',
                'Tél Personne Liée': client.tel_related_name || '',
                'Photo URL': client.photo_url || '',
                'Date Création': client.date_creation || '',
                'Date Modification': client.date_modification || ''
            }));

            // Créer le workbook Excel
            const workbook = XLSX.utils.book_new();
            const worksheet = XLSX.utils.json_to_sheet(excelData);

            // Ajuster la largeur des colonnes
            const columnWidths = [
                { wch: 12 }, { wch: 15 }, { wch: 15 }, { wch: 25 }, { wch: 15 },
                { wch: 15 }, { wch: 15 }, { wch: 30 }, { wch: 15 }, { wch: 10 },
                { wch: 12 }, { wch: 12 }, { wch: 12 }, { wch: 12 }, { wch: 10 },
                { wch: 8 }, { wch: 12 }, { wch: 12 }, { wch: 12 }, { wch: 12 },
                { wch: 10 }, { wch: 12 }, { wch: 12 }, { wch: 15 }, { wch: 15 },
                { wch: 15 }, { wch: 12 }, { wch: 20 }, { wch: 12 }, { wch: 15 },
                { wch: 30 }, { wch: 18 }, { wch: 18 }
            ];
            worksheet['!cols'] = columnWidths;

            // Ajouter la feuille au workbook
            XLSX.utils.book_append_sheet(workbook, worksheet, 'Clients');

            // Générer le fichier Excel en buffer
            const excelBuffer = XLSX.write(workbook, {
                type: 'buffer',
                bookType: 'xlsx',
                compression: true
            });

            // Définir les headers pour le téléchargement
            const fileName = `clients_export_${new Date().toISOString().split('T')[0]}.xlsx`;

            res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
            res.setHeader('Content-Length', excelBuffer.length);

            console.log(`✅ Export Excel généré: ${fileName} (${excelBuffer.length} bytes)`);

            // Envoyer le fichier
            res.send(excelBuffer);

        } catch (error) {
            console.error('❌ Erreur génération Excel:', error);
            res.status(500).json({ error: 'Erreur lors de la génération du fichier Excel: ' + error.message });
        }
    });
});

// GET /api/clients/search?q=terme - Rechercher des clients
router.get('/search', (req, res) => {
    const searchTerm = req.query.q;
    if (!searchTerm) {
        return res.status(400).json({ error: 'Terme de recherche requis' });
    }

    clientsDB.searchClients(searchTerm, (err, clients) => {
        if (err) {
            return res.status(500).json({ error: 'Erreur lors de la recherche' });
        }
        res.json(clients);
    });
});

// GET /api/clients/:id - Obtenir un client par ID
router.get('/:id', (req, res) => {
    const id = req.params.id;
    clientsDB.getClientWithRelations(id, (err, client) => {
        if (err) {
            return res.status(500).json({ error: 'Erreur lors de la récupération du client' });
        }
        if (!client) {
            return res.status(404).json({ error: 'Client non trouvé' });
        }
        res.json(client);
    });
});

// POST /api/clients - Ajouter un nouveau client
router.post('/', (req, res) => {
    const clientData = req.body;

    console.log('➕ POST /api/clients');
    console.log('📋 Données reçues:', JSON.stringify(clientData, null, 2));

    // Validation des champs requis
    if (!clientData.nom || !clientData.prenom || !clientData.email) {
        console.error('❌ Champs obligatoires manquants');
        return res.status(400).json({ error: 'Nom, prénom et email sont requis' });
    }

    console.log('✅ Validation réussie, appel addClient...');

    clientsDB.addClient(clientData, (err, clientId) => {
        if (err) {
            console.error('❌ Erreur addClient:', err);
            if (err.code === 'SQLITE_CONSTRAINT_UNIQUE') {
                return res.status(400).json({ error: 'Un client avec cet email existe déjà' });
            }
            return res.status(500).json({ error: 'Erreur lors de l\'ajout du client: ' + err.message });
        }
        console.log('✅ Client ajouté avec succès, ID:', clientId);
        res.status(201).json({ id: clientId, message: 'Client ajouté avec succès' });
    });
});

// PUT /api/clients/:id - Mettre à jour un client
router.put('/:id', (req, res) => {
    const id = req.params.id;
    const clientData = req.body;

    console.log(`🔄 PUT /api/clients/${id}`);
    console.log('📋 Données reçues:', JSON.stringify(clientData, null, 2));

    // Validation des données
    if (!clientData || typeof clientData !== 'object') {
        console.error('❌ Données invalides reçues');
        return res.status(400).json({ error: 'Données invalides' });
    }

    // Validation des champs obligatoires
    if (!clientData.nom || !clientData.prenom || !clientData.email) {
        console.error('❌ Champs obligatoires manquants:', {
            nom: !!clientData.nom,
            prenom: !!clientData.prenom,
            email: !!clientData.email
        });
        return res.status(400).json({ error: 'Les champs nom, prénom et email sont obligatoires' });
    }

    console.log('✅ Validation réussie, appel updateClient...');

    clientsDB.updateClient(id, clientData, (err) => {
        if (err) {
            console.error('❌ Erreur updateClient:', err);
            if (err.code === 'SQLITE_CONSTRAINT_UNIQUE') {
                return res.status(400).json({ error: 'Un client avec cet email existe déjà' });
            }
            return res.status(500).json({ error: 'Erreur lors de la mise à jour du client: ' + err.message });
        }
        console.log('✅ Client mis à jour avec succès');
        res.json({ message: 'Client mis à jour avec succès', id: id });
    });
});

// DELETE /api/clients/:id - Supprimer un client
router.delete('/:id', (req, res) => {
    const id = req.params.id;

    clientsDB.deleteClient(id, (err) => {
        if (err) {
            return res.status(500).json({ error: 'Erreur lors de la suppression du client' });
        }
        res.json({ message: 'Client supprimé avec succès' });
    });
});

module.exports = router;
