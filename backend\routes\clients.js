const express = require('express');
const router = express.Router();
const clientsDB = require('../database');

// GET /api/clients - Obtenir tous les clients
router.get('/', (req, res) => {
    clientsDB.getAllClients((err, clients) => {
        if (err) {
            return res.status(500).json({ error: 'Erreur lors de la récupération des clients' });
        }
        res.json(clients);
    });
});

// GET /api/clients/search?q=terme - Rechercher des clients
router.get('/search', (req, res) => {
    const searchTerm = req.query.q;
    if (!searchTerm) {
        return res.status(400).json({ error: 'Terme de recherche requis' });
    }

    clientsDB.searchClients(searchTerm, (err, clients) => {
        if (err) {
            return res.status(500).json({ error: 'Erreur lors de la recherche' });
        }
        res.json(clients);
    });
});

// GET /api/clients/:id - Obtenir un client par ID
router.get('/:id', (req, res) => {
    const id = req.params.id;
    clientsDB.getClientById(id, (err, client) => {
        if (err) {
            return res.status(500).json({ error: 'Erreur lors de la récupération du client' });
        }
        if (!client) {
            return res.status(404).json({ error: 'Client non trouvé' });
        }
        res.json(client);
    });
});

// POST /api/clients - Ajouter un nouveau client
router.post('/', (req, res) => {
    const { nom, prenom, email, telephone, adresse, ville, code_postal, photo_url } = req.body;

    // Validation des champs requis
    if (!nom || !prenom || !email) {
        return res.status(400).json({ error: 'Nom, prénom et email sont requis' });
    }

    const client = { nom, prenom, email, telephone, adresse, ville, code_postal, photo_url };

    clientsDB.addClient(client, (err, clientId) => {
        if (err) {
            if (err.code === 'SQLITE_CONSTRAINT_UNIQUE') {
                return res.status(400).json({ error: 'Un client avec cet email existe déjà' });
            }
            return res.status(500).json({ error: 'Erreur lors de l\'ajout du client' });
        }
        res.status(201).json({ id: clientId, message: 'Client ajouté avec succès' });
    });
});

// PUT /api/clients/:id - Mettre à jour un client
router.put('/:id', (req, res) => {
    const id = req.params.id;
    console.log('🔄 PUT /api/clients/' + id + ' - Données reçues:', req.body);

    const { nom, prenom, email, telephone, adresse, ville, code_postal, photo_url } = req.body;

    // Validation des champs requis
    if (!nom || !prenom || !email) {
        console.log('❌ Validation échouée - champs manquants');
        return res.status(400).json({ error: 'Nom, prénom et email sont requis' });
    }

    const client = { nom, prenom, email, telephone, adresse, ville, code_postal, photo_url };
    console.log('📋 Objet client à mettre à jour:', client);

    clientsDB.updateClient(id, client, (err) => {
        if (err) {
            console.error('❌ Erreur updateClient:', err);
            if (err.code === 'SQLITE_CONSTRAINT_UNIQUE') {
                return res.status(400).json({ error: 'Un client avec cet email existe déjà' });
            }
            return res.status(500).json({ error: 'Erreur lors de la mise à jour du client: ' + err.message });
        }
        console.log('✅ Client mis à jour avec succès');
        res.json({ message: 'Client mis à jour avec succès' });
    });
});

// DELETE /api/clients/:id - Supprimer un client
router.delete('/:id', (req, res) => {
    const id = req.params.id;

    clientsDB.deleteClient(id, (err) => {
        if (err) {
            return res.status(500).json({ error: 'Erreur lors de la suppression du client' });
        }
        res.json({ message: 'Client supprimé avec succès' });
    });
});

module.exports = router;
