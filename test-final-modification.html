<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 Test Final - Modifications</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        .test-section { margin: 25px 0; padding: 25px; border: 2px solid #e0e0e0; border-radius: 12px; background: #f8f9fa; }
        .result { margin: 15px 0; padding: 20px; border-radius: 8px; font-family: 'Courier New', monospace; white-space: pre-wrap; max-height: 400px; overflow-y: auto; font-size: 14px; }
        .success { background: #d4edda; color: #155724; border: 2px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 2px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 2px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 2px solid #ffeaa7; }
        button { padding: 15px 25px; margin: 10px; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; font-size: 14px; transition: all 0.3s ease; }
        .btn-primary { background: #007bff; color: white; }
        .btn-primary:hover { background: #0056b3; transform: translateY(-2px); }
        .btn-success { background: #28a745; color: white; }
        .btn-success:hover { background: #1e7e34; transform: translateY(-2px); }
        .btn-warning { background: #ffc107; color: black; }
        .btn-warning:hover { background: #e0a800; transform: translateY(-2px); }
        .btn-danger { background: #dc3545; color: white; }
        .btn-danger:hover { background: #c82333; transform: translateY(-2px); }
        h1 { color: #333; text-align: center; margin-bottom: 30px; font-size: 2.5rem; }
        h3 { color: #495057; border-bottom: 3px solid #007bff; padding-bottom: 10px; margin-bottom: 20px; }
        .progress { width: 100%; height: 25px; background: #e9ecef; border-radius: 15px; overflow: hidden; margin: 20px 0; }
        .progress-bar { height: 100%; background: linear-gradient(90deg, #007bff, #28a745); transition: width 0.5s ease; border-radius: 15px; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
        .stat-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 12px; text-align: center; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
        .stat-number { font-size: 2.5rem; font-weight: bold; margin-bottom: 5px; }
        .stat-label { font-size: 1rem; opacity: 0.9; }
        .test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .test-card { background: white; padding: 20px; border-radius: 10px; border: 1px solid #dee2e6; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Test Final - Modifications Client</h1>
        
        <!-- Progress Bar -->
        <div class="progress">
            <div class="progress-bar" id="progressBar" style="width: 0%"></div>
        </div>
        <div style="text-align: center; margin-bottom: 30px;">
            <span id="progressText">Prêt à commencer les tests</span>
        </div>

        <!-- Statistiques -->
        <div class="test-section">
            <h3>📊 État Actuel du Système</h3>
            <div class="stats" id="statsContainer">
                <div class="stat-card">
                    <div class="stat-number" id="totalClients">-</div>
                    <div class="stat-label">Clients Total</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="testsRun">0</div>
                    <div class="stat-label">Tests Exécutés</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="testsSuccess">0</div>
                    <div class="stat-label">Tests Réussis</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="testsFailed">0</div>
                    <div class="stat-label">Tests Échoués</div>
                </div>
            </div>
            <button class="btn-primary" onclick="loadSystemStats()">🔄 Actualiser Statistiques</button>
            <div id="statsResult" class="result"></div>
        </div>

        <!-- Tests Principaux -->
        <div class="test-section">
            <h3>🚀 Tests de Modification</h3>
            <div style="text-align: center; margin-bottom: 20px;">
                <button class="btn-success" onclick="runAllTests()" id="runAllBtn" style="font-size: 18px; padding: 20px 40px;">
                    🎯 LANCER TOUS LES TESTS
                </button>
                <button class="btn-warning" onclick="runQuickTest()">⚡ Test Rapide</button>
                <button class="btn-primary" onclick="runDetailedTest()">🔍 Test Détaillé</button>
            </div>
            <div id="mainTestResult" class="result"></div>
        </div>

        <!-- Tests Spécifiques -->
        <div class="test-section">
            <h3>🎯 Tests Spécifiques</h3>
            <div class="test-grid">
                <div class="test-card">
                    <h4>📝 Test Création</h4>
                    <button class="btn-success" onclick="testCreateClient()">Créer Client Test</button>
                    <div id="createResult" class="result"></div>
                </div>
                <div class="test-card">
                    <h4>✏️ Test Modification</h4>
                    <button class="btn-warning" onclick="testModifyClient()">Modifier Client</button>
                    <div id="modifyResult" class="result"></div>
                </div>
                <div class="test-card">
                    <h4>🔍 Test Vérification</h4>
                    <button class="btn-primary" onclick="testVerifyChanges()">Vérifier Changements</button>
                    <div id="verifyResult" class="result"></div>
                </div>
                <div class="test-card">
                    <h4>🗑️ Test Suppression</h4>
                    <button class="btn-danger" onclick="testDeleteClient()">Supprimer Client Test</button>
                    <div id="deleteResult" class="result"></div>
                </div>
            </div>
        </div>

        <!-- Interface Links -->
        <div class="test-section">
            <h3>🔗 Accès Interfaces</h3>
            <div style="text-align: center;">
                <button class="btn-primary" onclick="window.open('/', '_blank')" style="margin: 10px; padding: 20px 30px; font-size: 16px;">
                    🖥️ Interface Principale
                </button>
                <button class="btn-success" onclick="window.open('/test-debug-modification.html', '_blank')" style="margin: 10px; padding: 20px 30px; font-size: 16px;">
                    🐛 Debug Modification
                </button>
                <button class="btn-warning" onclick="window.open('/test-automatique.html', '_blank')" style="margin: 10px; padding: 20px 30px; font-size: 16px;">
                    🤖 Tests Automatiques
                </button>
            </div>
        </div>
    </div>

    <script>
        let testStats = {
            run: 0,
            success: 0,
            failed: 0
        };
        let lastCreatedClientId = null;

        // Charger les statistiques système
        async function loadSystemStats() {
            try {
                updateProgress(10, 'Chargement des statistiques...');
                
                const response = await fetch('/api/clients');
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                
                const clients = await response.json();
                
                document.getElementById('totalClients').textContent = clients.length;
                updateProgress(100, 'Statistiques chargées');
                
                addResult('statsResult', 'success', `✅ Système opérationnel
${clients.length} clients dans la base
API fonctionnelle
Serveur actif`);
                
                return clients;
                
            } catch (error) {
                updateProgress(0, 'Erreur chargement statistiques');
                addResult('statsResult', 'error', `❌ Erreur: ${error.message}`);
                return null;
            }
        }

        // Test complet
        async function runAllTests() {
            const btn = document.getElementById('runAllBtn');
            btn.disabled = true;
            btn.textContent = '🔄 Tests en cours...';
            
            testStats = { run: 0, success: 0, failed: 0 };
            updateTestStats();
            
            addResult('mainTestResult', 'info', '🚀 Démarrage de la suite de tests complète...\n');
            
            const tests = [
                { name: 'Chargement Système', func: loadSystemStats },
                { name: 'Création Client', func: testCreateClient },
                { name: 'Modification Client', func: testModifyClient },
                { name: 'Vérification Changements', func: testVerifyChanges },
                { name: 'Suppression Client', func: testDeleteClient }
            ];

            let allSuccess = true;

            for (let i = 0; i < tests.length; i++) {
                const test = tests[i];
                testStats.run++;
                updateTestStats();
                updateProgress((i / tests.length) * 100, `Test: ${test.name}...`);
                
                try {
                    addResult('mainTestResult', 'info', `🔄 ${test.name}...`);
                    await test.func();
                    testStats.success++;
                    addResult('mainTestResult', 'success', `✅ ${test.name}: RÉUSSI`);
                } catch (error) {
                    testStats.failed++;
                    allSuccess = false;
                    addResult('mainTestResult', 'error', `❌ ${test.name}: ÉCHEC - ${error.message}`);
                }
                
                updateTestStats();
                await sleep(1000);
            }

            updateProgress(100, 'Tests terminés');
            
            const finalMessage = allSuccess ? 
                '🎉 TOUS LES TESTS RÉUSSIS! Les modifications fonctionnent parfaitement!' :
                '⚠️ Certains tests ont échoué. Vérifiez les détails ci-dessus.';
                
            addResult('mainTestResult', allSuccess ? 'success' : 'warning', finalMessage);
            
            btn.disabled = false;
            btn.textContent = '🎯 LANCER TOUS LES TESTS';
        }

        // Test rapide
        async function runQuickTest() {
            try {
                updateProgress(25, 'Test rapide...');
                await testCreateClient();
                updateProgress(50, 'Modification...');
                await testModifyClient();
                updateProgress(75, 'Vérification...');
                await testVerifyChanges();
                updateProgress(100, 'Test rapide terminé');
                addResult('mainTestResult', 'success', '⚡ Test rapide: RÉUSSI');
            } catch (error) {
                updateProgress(0, 'Test rapide échoué');
                addResult('mainTestResult', 'error', `⚡ Test rapide: ÉCHEC - ${error.message}`);
            }
        }

        // Test détaillé
        async function runDetailedTest() {
            addResult('mainTestResult', 'info', '🔍 Test détaillé avec logs complets...');
            
            try {
                // Test avec tous les champs
                const detailedClient = {
                    nom: 'TEST_DETAILLE',
                    prenom: 'Client',
                    email: `test.detaille.${Date.now()}@example.com`,
                    telephone: '0123456789',
                    mobile_1: '0612345678',
                    mobile_2: '0687654321',
                    adresse: '123 Rue du Test Détaillé',
                    ville: 'Test City',
                    code_postal: '12345',
                    cin_no: 'AB123456',
                    passeport_no: 'M1234567',
                    nom_arabe: 'اختبار مفصل',
                    prenom_arabe: 'عميل'
                };

                addResult('mainTestResult', 'info', `📋 Création avec données complètes:\n${JSON.stringify(detailedClient, null, 2)}`);
                
                const createResponse = await fetch('/api/clients', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json; charset=utf-8' },
                    body: JSON.stringify(detailedClient)
                });

                if (!createResponse.ok) {
                    const error = await createResponse.text();
                    throw new Error(`Création échouée: ${error}`);
                }

                const createResult = await createResponse.json();
                lastCreatedClientId = createResult.id;
                
                addResult('mainTestResult', 'success', `✅ Client détaillé créé avec ID: ${createResult.id}`);
                
            } catch (error) {
                addResult('mainTestResult', 'error', `❌ Test détaillé: ${error.message}`);
            }
        }

        // Tests individuels
        async function testCreateClient() {
            const clientData = {
                nom: 'TEST_AUTO',
                prenom: 'Client',
                email: `test.auto.${Date.now()}@example.com`,
                telephone: '0123456789',
                ville: 'Test City'
            };

            const response = await fetch('/api/clients', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json; charset=utf-8' },
                body: JSON.stringify(clientData)
            });

            if (!response.ok) {
                const error = await response.text();
                throw new Error(`Création échouée: ${error}`);
            }

            const result = await response.json();
            lastCreatedClientId = result.id;
            
            addResult('createResult', 'success', `✅ Client créé avec ID: ${result.id}\nNom: ${clientData.nom} ${clientData.prenom}`);
            return result.id;
        }

        async function testModifyClient() {
            if (!lastCreatedClientId) {
                throw new Error('Aucun client à modifier. Créez d\'abord un client.');
            }

            const modificationData = {
                nom: 'TEST_MODIFIE',
                prenom: 'Client_Modifie',
                email: `test.modifie.${Date.now()}@example.com`,
                ville: 'Ville Modifiée',
                mobile_1: '0612345678',
                nom_arabe: 'اختبار معدل'
            };

            const response = await fetch(`/api/clients/${lastCreatedClientId}`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json; charset=utf-8' },
                body: JSON.stringify(modificationData)
            });

            if (!response.ok) {
                const error = await response.text();
                throw new Error(`Modification échouée: ${error}`);
            }

            const result = await response.json();
            
            addResult('modifyResult', 'success', `✅ Client ${lastCreatedClientId} modifié\nNouveau nom: ${modificationData.nom}\nNouvelle ville: ${modificationData.ville}`);
            return result;
        }

        async function testVerifyChanges() {
            if (!lastCreatedClientId) {
                throw new Error('Aucun client à vérifier.');
            }

            const response = await fetch(`/api/clients/${lastCreatedClientId}`);
            if (!response.ok) {
                throw new Error(`Vérification échouée: HTTP ${response.status}`);
            }

            const client = await response.json();
            
            const isModified = client.nom === 'TEST_MODIFIE' && client.ville === 'Ville Modifiée';
            
            if (isModified) {
                addResult('verifyResult', 'success', `✅ MODIFICATIONS CONFIRMÉES!\nNom: ${client.nom}\nVille: ${client.ville}\nMobile: ${client.mobile_1 || 'Non défini'}\nNom Arabe: ${client.nom_arabe || 'Non défini'}`);
            } else {
                throw new Error('Les modifications ne sont pas persistantes');
            }
            
            return client;
        }

        async function testDeleteClient() {
            if (!lastCreatedClientId) {
                throw new Error('Aucun client à supprimer.');
            }

            const response = await fetch(`/api/clients/${lastCreatedClientId}`, {
                method: 'DELETE'
            });

            if (!response.ok) {
                const error = await response.text();
                throw new Error(`Suppression échouée: ${error}`);
            }

            addResult('deleteResult', 'success', `✅ Client ${lastCreatedClientId} supprimé avec succès`);
            lastCreatedClientId = null;
        }

        // Utilitaires
        function updateProgress(percent, text = '') {
            document.getElementById('progressBar').style.width = `${percent}%`;
            document.getElementById('progressText').textContent = text || `${percent}%`;
        }

        function updateTestStats() {
            document.getElementById('testsRun').textContent = testStats.run;
            document.getElementById('testsSuccess').textContent = testStats.success;
            document.getElementById('testsFailed').textContent = testStats.failed;
        }

        function addResult(containerId, type, message) {
            const container = document.getElementById(containerId);
            const timestamp = new Date().toLocaleTimeString();
            const newMessage = `${timestamp} - ${message}`;
            
            if (container.textContent) {
                container.textContent += '\n' + newMessage;
            } else {
                container.textContent = newMessage;
            }
            
            container.className = `result ${type}`;
            container.scrollTop = container.scrollHeight;
        }

        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // Auto-start
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎯 Page de test final chargée');
            loadSystemStats();
        });
    </script>
</body>
</html>
