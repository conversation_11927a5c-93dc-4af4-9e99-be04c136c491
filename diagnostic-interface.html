<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Diagnostic Interface</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f0f0f0; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        .result { margin: 15px 0; padding: 20px; border-radius: 8px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto; }
        .success { background: #d4edda; color: #155724; border: 2px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 2px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 2px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 2px solid #ffeaa7; }
        button { padding: 15px 25px; margin: 10px; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; font-size: 16px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        h1 { color: #333; text-align: center; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #f9f9f9; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Diagnostic Interface Principale</h1>
        
        <div class="test-section">
            <h3>1. Test API Backend</h3>
            <button class="btn-primary" onclick="testAPI()">🔍 Tester API</button>
            <div id="apiResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. Test Éléments DOM</h3>
            <button class="btn-warning" onclick="testDOM()">🔍 Vérifier DOM</button>
            <div id="domResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. Test Chargement Clients</h3>
            <button class="btn-success" onclick="testLoadClients()">📋 Charger Clients</button>
            <div id="loadResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. Test Ajout Client</h3>
            <button class="btn-success" onclick="testAddClient()">➕ Ajouter Client Test</button>
            <div id="addResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>5. Test Interface Principale</h3>
            <button class="btn-primary" onclick="window.open('/', '_blank')" style="padding: 20px 30px; font-size: 18px;">
                🖥️ Ouvrir Interface Principale
            </button>
            <p>Ouvrez l'interface principale et vérifiez la console (F12) pour les erreurs.</p>
        </div>
    </div>

    <script>
        // Test API
        async function testAPI() {
            try {
                addResult('apiResult', 'info', 'Test de l\'API...');
                
                const response = await fetch('/api/clients');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const clients = await response.json();
                
                addResult('apiResult', 'success', `✅ API FONCTIONNELLE
Statut: ${response.status}
Nombre de clients: ${clients.length}
Clients trouvés: ${clients.map(c => `${c.nom} ${c.prenom}`).join(', ')}`);
                
            } catch (error) {
                addResult('apiResult', 'error', `❌ Erreur API: ${error.message}`);
            }
        }

        // Test éléments DOM
        function testDOM() {
            const elements = [
                'clientsContainer',
                'clientModal',
                'clientForm',
                'addClientBtn',
                'searchInput',
                'totalClients',
                'newClientsMonth'
            ];

            let results = '🔍 Vérification des éléments DOM:\n\n';
            let allFound = true;

            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    results += `✅ ${id}: TROUVÉ\n`;
                } else {
                    results += `❌ ${id}: MANQUANT\n`;
                    allFound = false;
                }
            });

            // Test des éléments de l'interface principale
            results += '\n🔍 Test depuis l\'interface principale:\n';
            
            // Simuler l'accès aux éléments comme dans script.js
            try {
                const iframe = document.createElement('iframe');
                iframe.src = '/';
                iframe.style.display = 'none';
                document.body.appendChild(iframe);
                
                iframe.onload = () => {
                    const iframeDoc = iframe.contentDocument;
                    const clientsContainer = iframeDoc.getElementById('clientsContainer');
                    
                    if (clientsContainer) {
                        results += '✅ clientsContainer trouvé dans l\'interface principale\n';
                    } else {
                        results += '❌ clientsContainer MANQUANT dans l\'interface principale\n';
                    }
                    
                    document.body.removeChild(iframe);
                    addResult('domResult', allFound ? 'success' : 'error', results);
                };
                
            } catch (error) {
                results += `❌ Erreur test iframe: ${error.message}\n`;
                addResult('domResult', allFound ? 'warning' : 'error', results);
            }
        }

        // Test chargement clients
        async function testLoadClients() {
            try {
                addResult('loadResult', 'info', 'Test du chargement des clients...');
                
                // Simuler la fonction loadClients
                const response = await fetch('/api/clients');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const clients = await response.json();
                
                // Simuler displayClients
                let displayHTML = '';
                if (clients.length === 0) {
                    displayHTML = `<div class="no-clients">
                        <h3>Aucun client trouvé</h3>
                        <p>Commencez par ajouter votre premier client !</p>
                    </div>`;
                } else {
                    displayHTML = clients.map(client => `
                        <div class="client-card">
                            <div class="client-name">${client.nom} ${client.prenom}</div>
                            <div class="client-email">${client.email}</div>
                            <div class="client-code">${client.code_client || 'Pas de code'}</div>
                        </div>
                    `).join('');
                }
                
                addResult('loadResult', 'success', `✅ CHARGEMENT RÉUSSI
Nombre de clients: ${clients.length}
HTML généré: ${displayHTML.length} caractères

Aperçu des clients:
${clients.map(c => `- ${c.nom} ${c.prenom} (${c.email})`).join('\n')}`);
                
            } catch (error) {
                addResult('loadResult', 'error', `❌ Erreur chargement: ${error.message}`);
            }
        }

        // Test ajout client
        async function testAddClient() {
            try {
                addResult('addResult', 'info', 'Test d\'ajout de client...');
                
                const clientData = {
                    nom: 'DIAGNOSTIC',
                    prenom: 'Test',
                    email: `diagnostic.test.${Date.now()}@example.com`,
                    telephone: '0123456789'
                };

                const response = await fetch('/api/clients', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json; charset=utf-8'
                    },
                    body: JSON.stringify(clientData)
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }

                const result = await response.json();
                
                addResult('addResult', 'success', `✅ CLIENT AJOUTÉ AVEC SUCCÈS
ID: ${result.id}
Message: ${result.message}

Données envoyées:
${JSON.stringify(clientData, null, 2)}`);
                
            } catch (error) {
                addResult('addResult', 'error', `❌ Erreur ajout: ${error.message}`);
            }
        }

        // Utilitaire
        function addResult(containerId, type, message) {
            const container = document.getElementById(containerId);
            container.className = `result ${type}`;
            container.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
        }

        // Auto-start
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🔍 Page de diagnostic chargée');
            testAPI(); // Test automatique de l'API
        });
    </script>
</body>
</html>
