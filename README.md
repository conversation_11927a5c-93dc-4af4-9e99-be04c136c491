# 🏢 Système de Gestion des Clients

Un système complet de gestion de clients avec photos de profil et gestion de documents (passeports, cartes d'identité, etc.).

## ✨ Fonctionnalités

### 👥 Gestion des Clients
- ➕ Ajouter de nouveaux clients
- ✏️ Modifier les informations des clients
- 🗑️ Supprimer des clients
- 🔍 Rechercher des clients par nom, prénom, email ou ville
- 📋 Afficher la liste complète des clients

### 📷 Gestion des Photos
- 📸 Ajouter une photo de profil pour chaque client
- 🖼️ Prévisualisation des photos dans l'interface
- 🔄 Modifier ou supprimer les photos existantes
- 📱 Affichage des photos dans les cartes clients

### 📄 Gestion des Documents
- 📤 Upload de documents pour chaque client
- 🛂 Support des types de documents :
  - Passeport
  - Carte d'identité
  - Permis de conduire
  - Autres documents
- 📥 Téléchargement des documents
- 🗑️ Suppression des documents
- 📊 Affichage de la taille des fichiers

## 🚀 Installation et Démarrage

### Prérequis
- Node.js (version 14 ou supérieure)
- npm

### Installation
```bash
# Installer les dépendances
npm install

# Démarrer le serveur
npm start
```

Le serveur sera accessible sur `http://localhost:3000`

## 📁 Structure du Projet

```
├── package.json                 # Configuration du projet
├── backend/
│   ├── server.js               # Serveur Express principal
│   ├── database.js             # Configuration SQLite et fonctions DB
│   ├── routes/
│   │   ├── clients.js          # Routes API pour les clients
│   │   └── upload.js           # Routes pour upload photos/documents
│   ├── uploads/                # Dossier des fichiers uploadés
│   │   ├── photos/             # Photos de profil
│   │   └── documents/          # Documents clients
│   └── clients.db              # Base de données SQLite (créée automatiquement)
└── frontend/
    ├── index.html              # Interface utilisateur
    ├── style.css               # Styles CSS
    └── script.js               # Logique JavaScript
```

## 🗄️ Base de Données

### Table `clients`
- `id` : Identifiant unique (auto-incrémenté)
- `nom` : Nom du client (requis)
- `prenom` : Prénom du client (requis)
- `email` : Email du client (requis, unique)
- `telephone` : Numéro de téléphone
- `adresse` : Adresse complète
- `ville` : Ville
- `code_postal` : Code postal
- `photo_url` : URL de la photo de profil
- `date_creation` : Date de création automatique
- `date_modification` : Date de dernière modification

### Table `documents`
- `id` : Identifiant unique (auto-incrémenté)
- `client_id` : Référence vers le client
- `type_document` : Type de document (passeport, carte_identite, etc.)
- `nom_fichier` : Nom original du fichier
- `chemin_fichier` : Chemin vers le fichier sur le serveur
- `taille_fichier` : Taille du fichier en octets
- `date_upload` : Date d'upload automatique

## 🔧 API Endpoints

### Clients
- `GET /api/clients` - Obtenir tous les clients
- `GET /api/clients/:id` - Obtenir un client par ID
- `GET /api/clients/search?q=terme` - Rechercher des clients
- `POST /api/clients` - Ajouter un nouveau client
- `PUT /api/clients/:id` - Mettre à jour un client
- `DELETE /api/clients/:id` - Supprimer un client

### Upload
- `POST /api/upload/photo/:clientId` - Upload photo de profil
- `POST /api/upload/document/:clientId` - Upload document
- `GET /api/upload/documents/:clientId` - Obtenir les documents d'un client
- `DELETE /api/upload/document/:documentId` - Supprimer un document
- `GET /api/upload/document/:documentId/download` - Télécharger un document

## 📝 Utilisation

### Ajouter un Client
1. Cliquer sur "➕ Ajouter un Client"
2. Remplir les informations obligatoires (nom, prénom, email)
3. Optionnellement, ajouter une photo de profil
4. Cliquer sur "💾 Enregistrer"

### Modifier un Client
1. Cliquer sur "✏️ Modifier" sur la carte du client
2. Modifier les informations souhaitées
3. Ajouter/modifier la photo si nécessaire
4. Gérer les documents dans la section dédiée
5. Cliquer sur "💾 Enregistrer"

### Gérer les Documents
1. En mode édition d'un client, utiliser la section "📄 Documents du Client"
2. Sélectionner le type de document
3. Choisir le fichier à uploader
4. Cliquer sur "📤 Ajouter Document"

## 🔒 Sécurité

- Validation des types de fichiers (images pour photos, PDF/images pour documents)
- Limitation de taille des fichiers (5MB pour photos, 10MB pour documents)
- Noms de fichiers uniques pour éviter les conflits
- Validation des données côté serveur

## 🎨 Interface

- Design moderne et responsive
- Interface intuitive avec icônes
- Messages de succès/erreur
- Prévisualisation des photos
- Recherche en temps réel

## 🛠️ Technologies Utilisées

- **Backend** : Node.js, Express.js, SQLite3, Multer
- **Frontend** : HTML5, CSS3, JavaScript (Vanilla)
- **Base de données** : SQLite
- **Upload de fichiers** : Multer avec stockage local

## 📞 Support

Pour toute question ou problème, veuillez consulter la documentation ou contacter l'équipe de développement.
