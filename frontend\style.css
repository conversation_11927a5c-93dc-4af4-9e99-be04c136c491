/* Reset et variables CSS */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Couleurs principales */
    --primary-color: #4f46e5;
    --primary-dark: #3730a3;
    --primary-light: #6366f1;
    --secondary-color: #06b6d4;
    --accent-color: #f59e0b;

    /* Couleurs neutres */
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* Couleurs d'état */
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #3b82f6;

    /* <PERSON>mbres */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    /* Rayons de bordure */
    --radius-sm: 0.375rem;
    --radius: 0.5rem;
    --radius-md: 0.75rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;

    /* Transitions */
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

body {
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: var(--gray-800);
    line-height: 1.6;
}

/* Navigation moderne */
.navbar {
    background: var(--white);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 100;
    border-bottom: 1px solid var(--gray-200);
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 4rem;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--primary-color);
}

.nav-brand i {
    font-size: 1.75rem;
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    text-decoration: none;
    color: var(--gray-600);
    font-weight: 500;
    border-radius: var(--radius);
    transition: var(--transition);
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
    background: var(--gray-50);
}

/* Container principal */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

/* Header moderne avec gradient */
.modern-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border-radius: var(--radius-xl);
    padding: 3rem;
    margin-bottom: 2rem;
    color: var(--white);
    box-shadow: var(--shadow-xl);
    position: relative;
    overflow: hidden;
}

.modern-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

.header-text h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-text p {
    font-size: 1.125rem;
    opacity: 0.9;
    font-weight: 300;
}

.header-stats {
    display: flex;
    gap: 2rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    text-align: center;
    min-width: 120px;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.875rem;
    opacity: 0.8;
    font-weight: 500;
}

/* Section de recherche moderne */
.search-section {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
}

.search-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.search-input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.search-icon {
    position: absolute;
    left: 1rem;
    color: var(--gray-400);
    font-size: 1.125rem;
    z-index: 1;
}

#searchInput {
    flex: 1;
    padding: 1rem 1rem 1rem 3rem;
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    font-size: 1rem;
    font-weight: 500;
    background: var(--gray-50);
    transition: var(--transition);
}

#searchInput:focus {
    outline: none;
    border-color: var(--primary-color);
    background: var(--white);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.clear-btn {
    position: absolute;
    right: 0.75rem;
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius);
    transition: var(--transition);
}

.clear-btn:hover {
    color: var(--gray-600);
    background: var(--gray-100);
}

.search-filters {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.filter-select {
    padding: 0.75rem 1rem;
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    background: var(--white);
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--gray-700);
    cursor: pointer;
    transition: var(--transition);
    min-width: 150px;
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* Barre d'actions moderne */
.actions-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    gap: 1rem;
    flex-wrap: wrap;
}

.actions-bar > div {
    display: flex;
    gap: 1rem;
}

/* Système de boutons moderne */
.btn-primary,
.btn-secondary,
.btn-outline,
.btn-danger,
.btn-success,
.btn-warning {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: 2px solid transparent;
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
    box-shadow: var(--shadow);
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: var(--gray-600);
    color: var(--white);
    box-shadow: var(--shadow);
}

.btn-secondary:hover {
    background: var(--gray-700);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-outline {
    background: var(--white);
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: var(--white);
}

.btn-danger {
    background: var(--error-color);
    color: var(--white);
    box-shadow: var(--shadow);
}

.btn-danger:hover {
    background: #dc2626;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-success {
    background: var(--success-color);
    color: var(--white);
    box-shadow: var(--shadow);
}

.btn-success:hover {
    background: #059669;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-icon {
    padding: 0.5rem;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
    border-radius: var(--radius);
    cursor: pointer;
    transition: var(--transition);
}

.btn-icon:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Boutons spéciaux */
.btn-photo,
.btn-photo-remove {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: var(--radius);
    font-size: 0.75rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.btn-photo {
    background: var(--primary-color);
    color: var(--white);
}

.btn-photo:hover {
    background: var(--primary-dark);
}

.btn-photo-remove {
    background: var(--error-color);
    color: var(--white);
}

.btn-photo-remove:hover {
    background: #dc2626;
}

.btn-upload {
    background: var(--success-color);
    color: var(--white);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-lg);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-upload:hover {
    background: #059669;
    transform: translateY(-2px);
}

/* Section des clients moderne */
.clients-section {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: 2rem;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
}

.clients-section h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--primary-color);
}

/* Cartes clients modernes */
.client-card {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-xl);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: var(--transition);
    display: flex;
    gap: 1.5rem;
    box-shadow: var(--shadow);
    position: relative;
    overflow: hidden;
}

.client-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.client-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.client-photo {
    flex-shrink: 0;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid var(--primary-color);
    background: var(--gray-100);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow);
}

.client-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.client-photo .no-photo {
    font-size: 1.5rem;
    color: var(--gray-400);
}

.client-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.client-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.client-name {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: 0.25rem;
}

.client-code {
    font-size: 0.75rem;
    color: var(--primary-color);
    font-weight: 600;
    background: rgba(79, 70, 229, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius);
}

.client-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-edit,
.btn-delete {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: var(--radius);
    cursor: pointer;
    font-size: 0.75rem;
    font-weight: 600;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.btn-edit {
    background: var(--success-color);
    color: var(--white);
}

.btn-edit:hover {
    background: #059669;
    transform: translateY(-1px);
}

.btn-delete {
    background: var(--error-color);
    color: var(--white);
}

.btn-delete:hover {
    background: #dc2626;
    transform: translateY(-1px);
}

.client-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.info-label {
    font-weight: 600;
    color: var(--gray-600);
    min-width: 80px;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.info-value {
    color: var(--gray-800);
    flex: 1;
}

/* Section documents */
.documents-section {
    margin-top: 2rem;
}

.document-upload-area {
    background: var(--gray-50);
    border: 2px dashed var(--gray-300);
    border-radius: var(--radius-lg);
    padding: 2rem;
    text-align: center;
    margin-bottom: 2rem;
    transition: var(--transition);
}

.document-upload-area:hover {
    border-color: var(--primary-color);
    background: rgba(79, 70, 229, 0.05);
}

.upload-zone {
    cursor: pointer;
    margin-bottom: 1rem;
}

.upload-zone i {
    font-size: 3rem;
    color: var(--gray-400);
    margin-bottom: 1rem;
}

.upload-zone h5 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
}

.upload-zone p {
    color: var(--gray-500);
    font-size: 0.875rem;
}

.upload-zone.dragover {
    border-color: var(--primary-color);
    background: rgba(79, 70, 229, 0.1);
    transform: scale(1.02);
}

.upload-zone.dragover i {
    color: var(--primary-color);
}

.upload-zone.dragover h5 {
    color: var(--primary-color);
}

.upload-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.document-type-select {
    padding: 0.75rem 1rem;
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    background: var(--white);
    font-weight: 500;
    cursor: pointer;
}

.documents-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
}

.document-item {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: var(--transition);
    box-shadow: var(--shadow-sm);
}

.document-item:hover {
    box-shadow: var(--shadow);
    transform: translateY(-2px);
}

.document-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex: 1;
}

.document-type {
    background: var(--primary-color);
    color: var(--white);
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius);
    font-size: 0.75rem;
    font-weight: 700;
}

.document-name {
    font-weight: 500;
    color: var(--gray-800);
    font-size: 0.875rem;
}

.document-size {
    font-size: 0.75rem;
    color: var(--gray-500);
}

.document-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-download,
.btn-view {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border: none;
    border-radius: var(--radius);
    cursor: pointer;
    font-weight: 600;
    transition: var(--transition);
}

.btn-download {
    background: var(--success-color);
    color: var(--white);
}

.btn-download:hover {
    background: #059669;
}

.btn-view {
    background: var(--info-color);
    color: var(--white);
}

.btn-view:hover {
    background: #2563eb;
}

/* Messages et états */
.loading {
    text-align: center;
    padding: 3rem;
    color: var(--gray-500);
    font-size: 1.125rem;
}

.no-clients {
    text-align: center;
    padding: 3rem;
    color: var(--gray-500);
}

.no-clients h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--gray-700);
}

.error-message {
    background: #fef2f2;
    color: #991b1b;
    padding: 1rem;
    border-radius: var(--radius-lg);
    margin-bottom: 1rem;
    border: 1px solid #fecaca;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.success-message {
    background: #f0fdf4;
    color: #166534;
    padding: 1rem;
    border-radius: var(--radius-lg);
    margin-bottom: 1rem;
    border: 1px solid #bbf7d0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .container {
        padding: 1rem;
    }

    .header-content {
        flex-direction: column;
        gap: 2rem;
        text-align: center;
    }

    .header-stats {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .nav-container {
        padding: 0 1rem;
    }

    .nav-menu {
        gap: 1rem;
    }

    .nav-link span {
        display: none;
    }

    .modern-header {
        padding: 2rem 1rem;
    }

    .header-text h1 {
        font-size: 2rem;
    }

    .header-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .search-container {
        gap: 1rem;
    }

    .search-filters {
        justify-content: center;
    }

    .actions-bar {
        flex-direction: column;
        align-items: stretch;
    }

    .client-card {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .client-header {
        flex-direction: column;
        gap: 1rem;
        align-items: center;
    }

    .client-info {
        grid-template-columns: 1fr;
        text-align: left;
    }

    .documents-grid {
        grid-template-columns: 1fr;
    }

    .upload-controls {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0.5rem;
    }

    .modern-header {
        padding: 1.5rem 1rem;
    }

    .header-text h1 {
        font-size: 1.5rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .search-section,
    .clients-section {
        padding: 1rem;
    }

    .client-card {
        padding: 1rem;
    }

    .stat-card {
        padding: 1rem;
        min-width: 100px;
    }

    .stat-number {
        font-size: 1.5rem;
    }
}

/* Animations et transitions */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.loading {
    animation: pulse 2s infinite;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.client-card {
    animation: slideInUp 0.3s ease-out;
}

/* Scrollbar personnalisée */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: var(--radius);
}

::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: var(--radius);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}

/* Utilitaires */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.hidden { display: none !important; }
.visible { display: block !important; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-4 { margin-top: 1rem; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }

.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-4 { padding: 1rem; }

.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }

.text-sm { font-size: 0.875rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }

/* Section des clients */
.clients-section {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.clients-section h2 {
    margin-bottom: 20px;
    color: #333;
    border-bottom: 2px solid #667eea;
    padding-bottom: 10px;
}

/* Cartes des clients */
.client-card {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
    display: flex;
    gap: 20px;
}

.client-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.client-photo {
    flex-shrink: 0;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid #667eea;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.client-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.client-photo .no-photo {
    font-size: 24px;
    color: #999;
}

.client-content {
    flex: 1;
}

.client-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.client-name {
    font-size: 1.3rem;
    font-weight: bold;
    color: #333;
}

.client-actions {
    display: flex;
    gap: 10px;
}

.btn-edit, .btn-delete {
    padding: 8px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.btn-edit {
    background: #28a745;
    color: white;
}

.btn-edit:hover {
    background: #218838;
}

.btn-delete {
    background: #dc3545;
    color: white;
}

.btn-delete:hover {
    background: #c82333;
}

.client-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.info-label {
    font-weight: 600;
    color: #666;
    min-width: 80px;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 2% auto;
    padding: 0;
    border-radius: 10px;
    width: 90%;
    max-width: 700px;
    max-height: 95vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    position: relative;
}

.modal-content.dragging {
    position: fixed;
    margin: 0;
    z-index: 1001;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
    background: #f8f9fa;
    border-radius: 10px 10px 0 0;
    cursor: move;
    user-select: none;
}

.modal-header:hover {
    background: #e9ecef;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.drag-handle {
    color: #6c757d;
    font-size: 16px;
    cursor: move;
    padding: 5px;
    border-radius: 3px;
    transition: all 0.3s ease;
}

.drag-handle:hover {
    background: #dee2e6;
    color: #495057;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
}

.close:hover {
    color: #000;
}

/* Formulaire */
form {
    padding: 20px;
}

/* Section Photo */
.photo-section {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.photo-upload-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.photo-preview {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 3px dashed #667eea;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    background: white;
}

.photo-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.photo-placeholder {
    color: #999;
    font-size: 14px;
    text-align: center;
}

/* Section Documents */
#documentsSection {
    margin-top: 20px;
    padding-top: 20px;
}

.document-upload {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.document-item {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.document-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.document-type {
    background: #667eea;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

.document-actions {
    display: flex;
    gap: 5px;
}

.btn-download, .btn-view {
    padding: 5px 10px;
    font-size: 12px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
}

.btn-download {
    background: #28a745;
    color: white;
}

.btn-view {
    background: #17a2b8;
    color: white;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
}

.form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
    padding: 20px;
    border-top: 1px solid #dee2e6;
    background: white;
    position: sticky;
    bottom: 0;
    border-radius: 0 0 10px 10px;
}

/* Messages */
.loading {
    text-align: center;
    padding: 40px;
    color: #666;
    font-size: 18px;
}

.no-clients {
    text-align: center;
    padding: 40px;
    color: #666;
}

.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    border: 1px solid #f5c6cb;
}

.success-message {
    background: #d4edda;
    color: #155724;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    border: 1px solid #c3e6cb;
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    header h1 {
        font-size: 2rem;
    }

    .search-container {
        flex-direction: column;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .client-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .client-info {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column;
    }

    .modal-content {
        width: 95%;
        margin: 1% auto;
        max-height: 98vh;
    }
}
