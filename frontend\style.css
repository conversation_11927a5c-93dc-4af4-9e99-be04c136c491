* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Section de recherche */
.search-section {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.search-container {
    display: flex;
    gap: 10px;
    align-items: center;
}

#searchInput {
    flex: 1;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
}

#searchInput:focus {
    outline: none;
    border-color: #667eea;
}

/* Actions */
.actions {
    text-align: center;
    margin-bottom: 30px;
}

/* Boutons */
.btn-primary, .btn-secondary, .btn-danger, #searchBtn, #clearSearchBtn {
    padding: 12px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary, #searchBtn {
    background: #667eea;
    color: white;
}

.btn-primary:hover, #searchBtn:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
}

.btn-secondary, #clearSearchBtn {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover, #clearSearchBtn:hover {
    background: #5a6268;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

/* Section des clients */
.clients-section {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.clients-section h2 {
    margin-bottom: 20px;
    color: #333;
    border-bottom: 2px solid #667eea;
    padding-bottom: 10px;
}

/* Cartes des clients */
.client-card {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
    display: flex;
    gap: 20px;
}

.client-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.client-photo {
    flex-shrink: 0;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid #667eea;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.client-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.client-photo .no-photo {
    font-size: 24px;
    color: #999;
}

.client-content {
    flex: 1;
}

.client-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.client-name {
    font-size: 1.3rem;
    font-weight: bold;
    color: #333;
}

.client-actions {
    display: flex;
    gap: 10px;
}

.btn-edit, .btn-delete {
    padding: 8px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.btn-edit {
    background: #28a745;
    color: white;
}

.btn-edit:hover {
    background: #218838;
}

.btn-delete {
    background: #dc3545;
    color: white;
}

.btn-delete:hover {
    background: #c82333;
}

.client-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.info-label {
    font-weight: 600;
    color: #666;
    min-width: 80px;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 2% auto;
    padding: 0;
    border-radius: 10px;
    width: 90%;
    max-width: 700px;
    max-height: 95vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
    background: #f8f9fa;
    border-radius: 10px 10px 0 0;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
}

.close:hover {
    color: #000;
}

/* Formulaire */
form {
    padding: 20px;
}

/* Section Photo */
.photo-section {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.photo-upload-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.photo-preview {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 3px dashed #667eea;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    background: white;
}

.photo-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.photo-placeholder {
    color: #999;
    font-size: 14px;
    text-align: center;
}

/* Section Documents */
#documentsSection {
    margin-top: 20px;
    padding-top: 20px;
}

.document-upload {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.document-item {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.document-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.document-type {
    background: #667eea;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

.document-actions {
    display: flex;
    gap: 5px;
}

.btn-download, .btn-view {
    padding: 5px 10px;
    font-size: 12px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
}

.btn-download {
    background: #28a745;
    color: white;
}

.btn-view {
    background: #17a2b8;
    color: white;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
}

.form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
    padding: 20px;
    border-top: 1px solid #dee2e6;
    background: white;
    position: sticky;
    bottom: 0;
    border-radius: 0 0 10px 10px;
}

/* Messages */
.loading {
    text-align: center;
    padding: 40px;
    color: #666;
    font-size: 18px;
}

.no-clients {
    text-align: center;
    padding: 40px;
    color: #666;
}

.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    border: 1px solid #f5c6cb;
}

.success-message {
    background: #d4edda;
    color: #155724;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    border: 1px solid #c3e6cb;
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    header h1 {
        font-size: 2rem;
    }

    .search-container {
        flex-direction: column;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .client-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .client-info {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column;
    }

    .modal-content {
        width: 95%;
        margin: 1% auto;
        max-height: 98vh;
    }
}
