<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Test Modal Corrigé</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f0f0f0; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        .result { margin: 15px 0; padding: 20px; border-radius: 8px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto; }
        .success { background: #d4edda; color: #155724; border: 2px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 2px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 2px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 2px solid #ffeaa7; }
        button { padding: 15px 25px; margin: 10px; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; font-size: 16px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        h1 { color: #333; text-align: center; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #f9f9f9; }
        .status-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .status-card { padding: 15px; border-radius: 8px; text-align: center; font-weight: bold; }
        .status-ok { background: #d4edda; color: #155724; }
        .status-error { background: #f8d7da; color: #721c24; }
        .status-unknown { background: #e2e3e5; color: #6c757d; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Test Modal Corrigé</h1>
        
        <div class="test-section">
            <h3>📊 Corrections Appliquées</h3>
            <div class="success result">
✅ Event listeners consolidés dans un seul DOMContentLoaded
✅ Event listeners dupliqués supprimés
✅ Fonction showClientPreview ajoutée
✅ Vérifications DOM renforcées
✅ Logs de debugging ajoutés
            </div>
        </div>

        <div class="test-section">
            <h3>1. Test Interface Principale</h3>
            <div class="status-grid">
                <div class="status-card status-unknown" id="statusInterface">Interface: Non testée</div>
                <div class="status-card status-unknown" id="statusModal">Modal: Non testé</div>
                <div class="status-card status-unknown" id="statusOnglets">Onglets: Non testés</div>
                <div class="status-card status-unknown" id="statusBoutons">Boutons: Non testés</div>
            </div>
            
            <button class="btn-primary" onclick="testInterface()">🖥️ Tester Interface</button>
            <button class="btn-success" onclick="window.open('/', '_blank')" style="padding: 20px 30px; font-size: 18px;">
                🚀 Ouvrir Interface Principale
            </button>
            <div id="interfaceResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. Test Modal et Onglets</h3>
            <button class="btn-warning" onclick="testModal()">🔍 Tester Modal</button>
            <div id="modalResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. Instructions de Test Manuel</h3>
            <div class="warning result">
📋 INSTRUCTIONS POUR TESTER LE MODAL:

1. Cliquez "Ouvrir Interface Principale"
2. Cliquez "Nouveau Client" ou "Modifier" sur un client existant
3. Vérifiez que le modal s'ouvre correctement
4. Testez TOUS les onglets:
   ✅ Général (doit être actif par défaut)
   ✅ Identité (documents d'identité)
   ✅ Contact (informations de contact)
   ✅ Voyage (compagnie aérienne, carte fidélité)
   ✅ Documents (upload de documents)

5. Testez TOUS les boutons:
   ✅ Aperçu (affiche un aperçu du client)
   ✅ Annuler (ferme le modal)
   ✅ Enregistrer (sauvegarde le client)
   ✅ X (ferme le modal)

6. Ouvrez la console (F12) et vérifiez les logs:
   - "🔧 Initialisation des onglets..."
   - "🔘 Bouton X: nom_onglet"
   - "🖱️ Clic sur onglet: nom_onglet"
   - "🔄 Changement d'onglet vers: nom_onglet"

RÉSULTAT ATTENDU:
✅ Tous les onglets s'affichent et sont cliquables
✅ Tous les boutons fonctionnent
✅ Le contenu change quand on clique sur les onglets
✅ Aucune erreur dans la console
            </div>
        </div>

        <div class="test-section">
            <h3>4. Test Ajout Client Complet</h3>
            <button class="btn-success" onclick="testAddClient()">➕ Test Ajout Complet</button>
            <div id="addResult" class="result"></div>
        </div>
    </div>

    <script>
        // Test interface
        async function testInterface() {
            try {
                addResult('interfaceResult', 'info', 'Test de l\'interface...');
                updateStatus('statusInterface', 'Test en cours...', 'unknown');
                
                // Test de base - vérifier que l'interface se charge
                const response = await fetch('/');
                if (!response.ok) {
                    throw new Error(`Interface non accessible: ${response.status}`);
                }
                
                updateStatus('statusInterface', 'Interface OK', 'ok');
                
                // Test avec iframe pour vérifier les éléments
                const iframe = document.createElement('iframe');
                iframe.src = '/';
                iframe.style.width = '1px';
                iframe.style.height = '1px';
                iframe.style.opacity = '0';
                iframe.style.position = 'absolute';
                iframe.style.top = '-1000px';
                document.body.appendChild(iframe);
                
                iframe.onload = () => {
                    try {
                        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        
                        // Vérifier les éléments du modal
                        const clientModal = iframeDoc.getElementById('clientModal');
                        const tabButtons = iframeDoc.querySelectorAll('.tab-btn');
                        const tabContents = iframeDoc.querySelectorAll('.tab-content');
                        const cancelBtn = iframeDoc.getElementById('cancelBtn');
                        const previewBtn = iframeDoc.getElementById('previewBtn');
                        
                        let details = '✅ Interface chargée\n';
                        
                        if (clientModal) {
                            details += '✅ Modal trouvé\n';
                            updateStatus('statusModal', 'Modal OK', 'ok');
                        } else {
                            details += '❌ Modal MANQUANT\n';
                            updateStatus('statusModal', 'Modal ERREUR', 'error');
                        }
                        
                        if (tabButtons.length > 0) {
                            details += `✅ ${tabButtons.length} onglets trouvés\n`;
                            updateStatus('statusOnglets', `${tabButtons.length} onglets`, 'ok');
                        } else {
                            details += '❌ Onglets MANQUANTS\n';
                            updateStatus('statusOnglets', 'Onglets ERREUR', 'error');
                        }
                        
                        if (cancelBtn && previewBtn) {
                            details += '✅ Boutons trouvés\n';
                            updateStatus('statusBoutons', 'Boutons OK', 'ok');
                        } else {
                            details += '❌ Boutons MANQUANTS\n';
                            updateStatus('statusBoutons', 'Boutons ERREUR', 'error');
                        }
                        
                        addResult('interfaceResult', 'success', details);
                        document.body.removeChild(iframe);
                        
                    } catch (error) {
                        addResult('interfaceResult', 'error', `❌ Erreur test iframe: ${error.message}`);
                        document.body.removeChild(iframe);
                    }
                };
                
            } catch (error) {
                updateStatus('statusInterface', 'Interface ERREUR', 'error');
                addResult('interfaceResult', 'error', `❌ Erreur: ${error.message}`);
            }
        }

        // Test modal spécifique
        async function testModal() {
            try {
                addResult('modalResult', 'info', 'Test du modal...');
                
                // Simuler l'ouverture du modal via l'API
                const response = await fetch('/api/clients');
                if (!response.ok) {
                    throw new Error(`API non accessible: ${response.status}`);
                }
                
                const clients = await response.json();
                
                addResult('modalResult', 'success', `✅ Test modal préparé
${clients.length} clients disponibles pour test
Ouvrez l'interface principale et testez:
- Cliquer "Nouveau Client"
- Cliquer "Modifier" sur un client
- Naviguer entre les onglets
- Tester tous les boutons`);
                
            } catch (error) {
                addResult('modalResult', 'error', `❌ Erreur test modal: ${error.message}`);
            }
        }

        // Test ajout client
        async function testAddClient() {
            try {
                addResult('addResult', 'info', 'Test d\'ajout client...');
                
                const clientData = {
                    nom: 'TEST_MODAL',
                    prenom: 'Correction',
                    email: `test.modal.${Date.now()}@example.com`,
                    telephone: '0123456789',
                    ville: 'Test City'
                };

                const response = await fetch('/api/clients', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json; charset=utf-8'
                    },
                    body: JSON.stringify(clientData)
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }

                const result = await response.json();
                
                addResult('addResult', 'success', `✅ CLIENT AJOUTÉ AVEC SUCCÈS
ID: ${result.id}
Message: ${result.message}

Maintenant testez la modification de ce client dans l'interface!`);
                
            } catch (error) {
                addResult('addResult', 'error', `❌ Erreur ajout: ${error.message}`);
            }
        }

        // Utilitaires
        function updateStatus(elementId, text, type) {
            const element = document.getElementById(elementId);
            element.textContent = text;
            element.className = `status-card status-${type}`;
        }

        function addResult(containerId, type, message) {
            const container = document.getElementById(containerId);
            container.className = `result ${type}`;
            container.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
        }

        // Auto-start
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🔧 Page de test modal chargée');
            testInterface(); // Test automatique
        });
    </script>
</body>
</html>
