<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Test Téléchargement Documents</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f0f0f0; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        .result { margin: 15px 0; padding: 20px; border-radius: 8px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto; }
        .success { background: #d4edda; color: #155724; border: 2px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 2px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 2px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 2px solid #ffeaa7; }
        button { padding: 15px 25px; margin: 10px; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; font-size: 16px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        h1 { color: #333; text-align: center; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #f9f9f9; }
        .client-list { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; max-height: 200px; overflow-y: auto; }
        .client-item { padding: 10px; margin: 5px 0; background: white; border-radius: 5px; border: 1px solid #ddd; cursor: pointer; }
        .client-item:hover { background: #e9ecef; }
        .client-item.selected { background: #007bff; color: white; }
        .document-list { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .document-item { padding: 15px; margin: 10px 0; background: white; border-radius: 5px; border: 1px solid #ddd; display: flex; justify-content: space-between; align-items: center; }
        .document-info { flex: 1; }
        .document-actions { display: flex; gap: 10px; }
        .upload-zone { border: 2px dashed #ddd; padding: 30px; text-align: center; border-radius: 8px; margin: 15px 0; cursor: pointer; }
        .upload-zone:hover { border-color: #007bff; background: rgba(0, 123, 255, 0.1); }
        .step { background: #e9ecef; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Test Téléchargement Documents - Correction Appliquée</h1>
        
        <div class="test-section">
            <h3>📊 Problème Identifié et Corrigé</h3>
            <div class="success result">
✅ PROBLÈME: Après upload, boutons téléchargement ne fonctionnent plus
✅ CAUSE: Event listeners non attachés aux nouveaux éléments DOM
✅ SOLUTION: Fonction attachDocumentEventListeners() ajoutée
✅ AMÉLIORATION: Logs détaillés et gestion d'erreurs renforcée
            </div>
        </div>

        <div class="test-section">
            <h3>1. Sélection du Client</h3>
            <button class="btn-primary" onclick="loadClients()">📋 Charger Clients</button>
            <div id="clientsList" class="client-list"></div>
            <div id="selectedClient" class="info result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>2. Test Complet du Cycle Documents</h3>
            <div id="testSection" style="display: none;">
                
                <!-- Upload -->
                <div class="step">
                    <h4>Étape 1: Upload de Document</h4>
                    <div class="upload-zone" id="uploadZone">
                        <i class="fas fa-cloud-upload-alt" style="font-size: 2rem; color: #6c757d; margin-bottom: 10px;"></i>
                        <h5>Glisser-déposer un fichier ici</h5>
                        <input type="file" id="documentInput" accept=".pdf,.jpg,.jpeg,.png,.gif" style="margin: 10px 0;">
                    </div>
                    <select id="documentType" style="padding: 8px; margin: 5px; border-radius: 4px;">
                        <option value="passeport">🛂 Passeport</option>
                        <option value="carte_identite">🆔 Carte d'identité</option>
                        <option value="autre">📄 Autre document</option>
                    </select>
                    <button class="btn-success" onclick="uploadDocument()">📤 Upload</button>
                </div>

                <!-- Liste des documents -->
                <div class="step">
                    <h4>Étape 2: Liste des Documents</h4>
                    <button class="btn-primary" onclick="loadDocuments()">🔄 Actualiser Liste</button>
                    <div id="documentsList" class="document-list"></div>
                </div>

                <!-- Test téléchargement -->
                <div class="step">
                    <h4>Étape 3: Test Téléchargement</h4>
                    <div class="warning result">
📋 INSTRUCTIONS DE TEST:

1. Uploadez un document (étape 1)
2. Vérifiez qu'il apparaît dans la liste (étape 2)
3. Cliquez IMMÉDIATEMENT sur "Télécharger" (sans recharger)
4. Le téléchargement doit fonctionner
5. Uploadez un autre document
6. Testez à nouveau le téléchargement
7. Répétez plusieurs fois

RÉSULTAT ATTENDU:
✅ Téléchargement fonctionne après chaque upload
✅ Pas besoin de recharger la page
✅ Logs visibles dans la console (F12)
                    </div>
                </div>
            </div>
            <div id="testResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. Interface Principale</h3>
            <button class="btn-primary" onclick="window.open('/', '_blank')" style="padding: 20px 30px; font-size: 18px;">
                🖥️ Ouvrir Interface Principale
            </button>
            <div class="warning result">
📋 TEST DANS L'INTERFACE PRINCIPALE:

1. Ouvrez l'interface principale
2. Modifiez un client → Onglet Documents
3. Uploadez un document
4. Testez IMMÉDIATEMENT le téléchargement
5. Uploadez un autre document
6. Testez à nouveau le téléchargement

VÉRIFICATIONS:
✅ Console (F12): Logs "🔗 Attachement des event listeners"
✅ Console (F12): Logs "⬇️ Téléchargement du document ID: X"
✅ Téléchargement fonctionne sans recharger
            </div>
        </div>
    </div>

    <script>
        let selectedClientId = null;
        let clientDocuments = [];

        // Charger les clients
        async function loadClients() {
            try {
                const response = await fetch('/api/clients');
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                
                const clients = await response.json();
                
                const clientsList = document.getElementById('clientsList');
                clientsList.innerHTML = clients.map(client => `
                    <div class="client-item" onclick="selectClient(${client.id}, '${client.nom}', '${client.prenom}')">
                        <strong>${client.nom} ${client.prenom}</strong><br>
                        <small>${client.email}</small>
                    </div>
                `).join('');
                
                addResult('testResult', 'success', `${clients.length} clients chargés`);
                
            } catch (error) {
                addResult('testResult', 'error', `Erreur: ${error.message}`);
            }
        }

        // Sélectionner un client
        function selectClient(clientId, nom, prenom) {
            selectedClientId = clientId;
            
            document.querySelectorAll('.client-item').forEach(item => {
                item.classList.remove('selected');
            });
            event.target.classList.add('selected');
            
            const selectedDiv = document.getElementById('selectedClient');
            selectedDiv.style.display = 'block';
            selectedDiv.textContent = `Client sélectionné: ${nom} ${prenom} (ID: ${clientId})`;
            selectedDiv.className = 'success result';
            
            document.getElementById('testSection').style.display = 'block';
            
            initializeDragDrop();
            loadDocuments();
        }

        // Initialiser drag & drop
        function initializeDragDrop() {
            const uploadZone = document.getElementById('uploadZone');
            const documentInput = document.getElementById('documentInput');
            
            uploadZone.onclick = () => documentInput.click();
            
            uploadZone.ondragover = (e) => {
                e.preventDefault();
                uploadZone.style.borderColor = '#007bff';
                uploadZone.style.background = 'rgba(0, 123, 255, 0.1)';
            };
            
            uploadZone.ondragleave = (e) => {
                e.preventDefault();
                uploadZone.style.borderColor = '#ddd';
                uploadZone.style.background = '';
            };
            
            uploadZone.ondrop = (e) => {
                e.preventDefault();
                uploadZone.style.borderColor = '#ddd';
                uploadZone.style.background = '';
                documentInput.files = e.dataTransfer.files;
                if (documentInput.files.length > 0) {
                    uploadDocument();
                }
            };
        }

        // Upload document
        async function uploadDocument() {
            if (!selectedClientId) {
                addResult('testResult', 'error', 'Aucun client sélectionné');
                return;
            }

            const files = document.getElementById('documentInput').files;
            if (!files || files.length === 0) {
                addResult('testResult', 'error', 'Aucun fichier sélectionné');
                return;
            }

            try {
                addResult('testResult', 'info', `Upload de ${files.length} fichier(s)...`);
                
                for (let i = 0; i < files.length; i++) {
                    const file = files[i];
                    const formData = new FormData();
                    formData.append('document', file);
                    formData.append('type_document', document.getElementById('documentType').value);

                    const response = await fetch(`/api/upload/document/${selectedClientId}`, {
                        method: 'POST',
                        body: formData
                    });

                    if (!response.ok) {
                        const error = await response.json();
                        throw new Error(error.error || 'Erreur upload');
                    }
                }

                addResult('testResult', 'success', `✅ Upload réussi! Testez maintenant le téléchargement IMMÉDIATEMENT`);
                document.getElementById('documentInput').value = '';
                
                // Recharger et attacher les event listeners
                await loadDocuments();
                
            } catch (error) {
                addResult('testResult', 'error', `❌ Erreur: ${error.message}`);
            }
        }

        // Charger les documents avec event listeners
        async function loadDocuments() {
            if (!selectedClientId) return;

            try {
                const response = await fetch(`/api/upload/documents/${selectedClientId}`);
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                
                clientDocuments = await response.json();
                
                const documentsList = document.getElementById('documentsList');
                if (clientDocuments.length === 0) {
                    documentsList.innerHTML = '<div style="text-align: center; color: #6c757d; padding: 20px;">Aucun document</div>';
                } else {
                    documentsList.innerHTML = clientDocuments.map(doc => `
                        <div class="document-item">
                            <div class="document-info">
                                <strong>${getDocumentTypeLabel(doc.type_document)}</strong><br>
                                <small>${doc.nom_fichier} (${formatFileSize(doc.taille_fichier)})</small>
                            </div>
                            <div class="document-actions">
                                <button class="btn-primary btn-download" data-doc-id="${doc.id}">⬇️ Télécharger</button>
                                <button class="btn-danger btn-delete" data-doc-id="${doc.id}">🗑️ Supprimer</button>
                            </div>
                        </div>
                    `).join('');
                    
                    // IMPORTANT: Attacher les event listeners aux nouveaux boutons
                    attachEventListeners();
                }
                
                addResult('testResult', 'info', `${clientDocuments.length} document(s) chargé(s) avec event listeners attachés`);
                
            } catch (error) {
                addResult('testResult', 'error', `Erreur: ${error.message}`);
            }
        }

        // Attacher les event listeners (CORRECTION PRINCIPALE)
        function attachEventListeners() {
            console.log('🔗 Attachement des event listeners...');
            
            // Boutons de téléchargement
            const downloadButtons = document.querySelectorAll('.btn-download');
            downloadButtons.forEach(button => {
                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    const docId = button.getAttribute('data-doc-id');
                    console.log('⬇️ Téléchargement document ID:', docId);
                    downloadDocument(parseInt(docId));
                });
            });

            // Boutons de suppression
            const deleteButtons = document.querySelectorAll('.btn-delete');
            deleteButtons.forEach(button => {
                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    const docId = button.getAttribute('data-doc-id');
                    console.log('🗑️ Suppression document ID:', docId);
                    deleteDocument(parseInt(docId));
                });
            });

            console.log('✅ Event listeners attachés:', downloadButtons.length, 'téléchargements,', deleteButtons.length, 'suppressions');
        }

        // Télécharger document (CORRIGÉ)
        function downloadDocument(documentId) {
            console.log('⬇️ Téléchargement du document ID:', documentId);
            
            if (!documentId) {
                addResult('testResult', 'error', 'ID document manquant');
                return;
            }

            try {
                const downloadUrl = `/api/upload/document/${documentId}/download`;
                console.log('🔗 URL de téléchargement:', downloadUrl);
                
                // Créer un lien temporaire
                const link = document.createElement('a');
                link.href = downloadUrl;
                link.target = '_blank';
                link.download = '';
                
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                console.log('✅ Téléchargement initié');
                addResult('testResult', 'success', `✅ Téléchargement initié pour document ${documentId}`);
                
            } catch (error) {
                console.error('❌ Erreur téléchargement:', error);
                addResult('testResult', 'error', `❌ Erreur: ${error.message}`);
            }
        }

        // Supprimer document
        async function deleteDocument(documentId) {
            if (!confirm('Supprimer ce document ?')) return;

            try {
                const response = await fetch(`/api/upload/document/${documentId}`, {
                    method: 'DELETE'
                });

                if (!response.ok) throw new Error(`HTTP ${response.status}`);

                addResult('testResult', 'success', 'Document supprimé');
                loadDocuments();
                
            } catch (error) {
                addResult('testResult', 'error', `Erreur: ${error.message}`);
            }
        }

        // Utilitaires
        function getDocumentTypeLabel(type) {
            const types = {
                'passeport': '🛂 Passeport',
                'carte_identite': '🆔 Carte d\'identité',
                'autre': '📄 Autre'
            };
            return types[type] || '📄 Document';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function addResult(containerId, type, message) {
            const container = document.getElementById(containerId);
            container.className = `result ${type}`;
            container.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
        }

        // Auto-start
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🔧 Page de test téléchargement chargée');
            loadClients();
        });
    </script>
</body>
</html>
